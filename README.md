# Augment UI

A flexible React component library built with design tokens, Tailwind CSS, and TypeScript. Inspired by Chakra UI's component patterns with modern styling and accessibility features.

## Workspace Structure

This is a monorepo using npm workspaces with the following packages:

- **`packages/ui`** - The main component library (`@augment/ui`)
- **`packages/demo`** - Demo application showcasing the components
- **`packages/figma-plugin`** - Figma plugin for exporting design tokens (`@augment/figma-plugin`)
- **`packages/figma-alias-plugin`** - Figma plugin for managing variable aliases (`@augment/figma-alias-plugin`)

## Features

- 🎨 **Design Tokens**: Comprehensive design system with consistent spacing, colors, typography, and more
- 🎯 **TypeScript**: Full TypeScript support with excellent type safety
- 🌊 **Tailwind CSS**: Built on Tailwind CSS for utility-first styling
- ♿ **Accessible**: Components built with accessibility in mind
- 🎭 **Themeable**: Customizable theme system with CSS custom properties
- 📦 **Tree Shakeable**: Import only what you need
- 🔧 **Developer Experience**: Great DX with IntelliSense and documentation

## Quick Start

### For Users

```bash
npm install @augment/ui
# or
yarn add @augment/ui
# or
pnpm add @augment/ui
```

## Quick Start

1. Wrap your app with the `ThemeProvider`:

```tsx
import { ThemeProvider } from '@augment/ui'
import '@augment/ui/styles'

function App() {
  return (
    <ThemeProvider>
      {/* Your app content */}
    </ThemeProvider>
  )
}
```

2. Start using components:

```tsx
import { Button, Stack, Heading, Text } from '@augment/ui'

function MyComponent() {
  return (
    <Stack spacing={4}>
      <Heading>Welcome to Augment UI</Heading>
      <Text>A modern React component library</Text>
      <Button variant="primary">Get Started</Button>
    </Stack>
  )
}
```

## Components

### Layout
- `Box` - The fundamental layout component
- `Flex` - Flexbox container with convenient props
- `Stack` / `HStack` - Vertical and horizontal stacks
- `Grid` / `GridItem` - CSS Grid layouts
- `Container` - Responsive container with max-width
- `Divider` - Visual content separator
- `Card` - Content container with variants
- `Spacer` - Flexible spacing component
- `Center` - Center content horizontally and vertically

### Typography
- `Text` - Text component with variants
- `Heading` - Semantic headings (h1-h6)
- `Code` - Inline and block code display
- `Link` - Styled links with external link support

### Forms
- `Button` - Interactive buttons with variants
- `Input` - Text input with icons and validation states
- `Textarea` - Multi-line text input
- `Checkbox` - Checkbox input with labels
- `Radio` / `RadioGroup` - Radio button inputs
- `Label` - Form field labels
- `FormField` - Complete form field with label, input, and error handling

### Feedback
- `Alert` - Alert messages with variants
- `Badge` - Status and label badges
- `Spinner` - Loading spinners
- `Progress` - Progress bars and indicators
- `Skeleton` - Loading placeholders

## Theming

Augment UI uses a comprehensive design token system. You can customize the theme by passing a custom theme to the `ThemeProvider`:

```tsx
import { ThemeProvider, defaultTheme } from '@augment/ui'

const customTheme = {
  ...defaultTheme,
  colors: {
    ...defaultTheme.colors,
    primary: {
      ...defaultTheme.colors.primary,
      500: '#your-brand-color',
    },
  },
}

function App() {
  return (
    <ThemeProvider theme={customTheme}>
      {/* Your app */}
    </ThemeProvider>
  )
}
```

## Design Tokens

The library includes a comprehensive set of design tokens:

- **Colors**: Primary, secondary, success, warning, error, and neutral color scales
- **Spacing**: Consistent spacing scale from 0 to 96
- **Typography**: Font sizes, weights, and line heights
- **Border Radius**: Consistent border radius values
- **Shadows**: Elevation shadows
- **Breakpoints**: Responsive breakpoints

### For Developers

```bash
# Clone the repository
git clone https://github.com/your-username/augment-ui.git
cd augment-ui

# Install dependencies for all workspaces
npm install

# Start the demo application
npm run dev

# Build the component library
npm run build

# Build all packages
npm run build:all
```

## Workspace Commands

### Root Level Commands

```bash
# Start demo application
npm run dev

# Start UI library in watch mode
npm run dev:ui

# Start Figma plugin development
npm run dev:figma

# Start Figma alias plugin development
npm run dev:figma-alias

# Build component library
npm run build

# Build demo application
npm run build:demo

# Build Figma plugin
npm run build:figma

# Build Figma alias plugin
npm run build:figma-alias

# Build all packages
npm run build:all

# Clean all packages
npm run clean

# Run tests in all packages
npm run test

# Run type checking in all packages
npm run typecheck
```

### Package-Specific Commands

```bash
# Work with the UI library
npm run build --workspace=@augment/ui
npm run dev --workspace=@augment/ui

# Work with the demo
npm run dev --workspace=@augment/demo
npm run build --workspace=@augment/demo

# Work with the Figma plugin
npm run dev --workspace=@augment/figma-plugin
npm run build --workspace=@augment/figma-plugin

# Work with the Figma alias plugin
npm run dev --workspace=@augment/figma-alias-plugin
npm run build --workspace=@augment/figma-alias-plugin
```

## Contributing

We welcome contributions! Please see our contributing guidelines for more details.

## License

MIT License - see LICENSE file for details.
