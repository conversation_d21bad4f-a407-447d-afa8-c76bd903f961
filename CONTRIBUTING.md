# Contributing to Augment UI

Thank you for your interest in contributing to Augment UI! This guide will help you get started.

## Development Setup

1. **Clone the repository**
   ```bash
   git clone https://github.com/your-username/augment-ui.git
   cd augment-ui
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start the development server**
   ```bash
   npm run dev
   ```

4. **Open your browser**
   Navigate to `http://localhost:5173` to see the demo application.

## Project Structure

```
src/
├── components/          # React components
│   ├── feedback/       # Alert, Badge, Spinner, etc.
│   ├── forms/          # Button, Input, Checkbox, etc.
│   ├── layout/         # Box, Flex, Grid, etc.
│   └── typography/     # Text, Heading, Link, etc.
├── theme/              # Design system and theming
│   ├── tokens.ts       # Design tokens
│   ├── theme.ts        # Theme configuration
│   ├── ThemeProvider.tsx # Theme context provider
│   └── css-vars.ts     # CSS custom properties
├── utils/              # Utility functions
│   ├── cn.ts           # Class name utility
│   └── variants.ts     # Component variants
├── styles/             # Global styles
└── index.ts            # Main export file
```

## Component Guidelines

### 1. Component Structure

Each component should follow this structure:

```tsx
import React from 'react'
import { cn } from '../../utils/cn'

export interface ComponentProps extends React.HTMLAttributes<HTMLDivElement> {
  /**
   * Component-specific props with JSDoc comments
   */
  variant?: 'default' | 'primary' | 'secondary'
  size?: 'sm' | 'md' | 'lg'
  className?: string
  children?: React.ReactNode
}

/**
 * Component description with usage examples
 */
export const Component = React.forwardRef<HTMLDivElement, ComponentProps>(
  ({ variant = 'default', size = 'md', className, children, ...props }, ref) => {
    const componentClasses = cn(
      'base-classes',
      {
        // Conditional classes based on props
        'variant-classes': variant === 'primary',
        'size-classes': size === 'lg',
      },
      className
    )

    return (
      <div
        ref={ref}
        className={componentClasses}
        {...props}
      >
        {children}
      </div>
    )
  }
)

Component.displayName = 'Component'
```

### 2. TypeScript

- Use TypeScript for all components
- Provide comprehensive prop interfaces with JSDoc comments
- Export prop types for external use
- Use generic types where appropriate

### 3. Styling

- Use Tailwind CSS classes for styling
- Leverage the `cn()` utility for conditional classes
- Follow the design token system
- Use class-variance-authority for complex variants

### 4. Accessibility

- Include proper ARIA attributes
- Support keyboard navigation
- Provide semantic HTML elements
- Test with screen readers

### 5. Forwarded Refs

- Use `React.forwardRef` for all components
- Forward refs to the appropriate DOM element
- Set `displayName` for better debugging

## Adding New Components

1. **Create the component file**
   ```bash
   # For layout components
   src/components/layout/NewComponent.tsx
   
   # For form components
   src/components/forms/NewComponent.tsx
   
   # etc.
   ```

2. **Implement the component**
   Follow the component guidelines above.

3. **Add to index file**
   Export the component from the appropriate index file:
   ```tsx
   // src/components/layout/index.ts
   export { NewComponent } from './NewComponent'
   export type { NewComponentProps } from './NewComponent'
   ```

4. **Update main index**
   Add the component to the main export file:
   ```tsx
   // src/index.ts
   export { NewComponent } from './components/layout'
   export type { NewComponentProps } from './components/layout'
   ```

5. **Add to demo**
   Include the component in the demo application to showcase its usage.

## Design Tokens

When adding new design tokens:

1. **Update tokens.ts**
   Add new tokens to the appropriate section.

2. **Update CSS variables**
   Add corresponding CSS custom properties in `css-vars.ts`.

3. **Update Tailwind config**
   Extend the Tailwind configuration if needed.

## Testing

Currently, the project uses manual testing through the demo application. When adding new components:

1. Add examples to the demo application
2. Test all variants and states
3. Test accessibility with keyboard navigation
4. Test responsive behavior

## Building

```bash
# Build the library
npm run build

# Build and watch for changes
npm run build:watch

# Build only TypeScript declarations
npm run build:types
```

## Pull Request Process

1. **Fork the repository**
2. **Create a feature branch**
   ```bash
   git checkout -b feature/new-component
   ```

3. **Make your changes**
   Follow the guidelines above.

4. **Test your changes**
   Ensure the demo application works correctly.

5. **Update documentation**
   Add or update relevant documentation.

6. **Commit your changes**
   Use clear, descriptive commit messages.

7. **Push and create a pull request**
   Provide a clear description of your changes.

## Code Style

- Use Prettier for code formatting
- Follow ESLint rules
- Use meaningful variable and function names
- Write clear comments for complex logic
- Keep components focused and single-purpose

## Questions?

If you have questions or need help, please:

1. Check the existing documentation
2. Look at similar components for patterns
3. Open an issue for discussion
4. Reach out to the maintainers

Thank you for contributing to Augment UI!
