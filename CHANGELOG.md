# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [0.1.0] - 2025-06-26

### Added

#### Core System
- **Design Token System**: Comprehensive design tokens for colors, spacing, typography, shadows, and more
- **Theme Provider**: React context provider for theme management with CSS custom properties
- **TypeScript Support**: Full TypeScript support with excellent type safety
- **Tailwind CSS Integration**: Built on Tailwind CSS v4 with PostCSS support

#### Layout Components
- **Box**: Fundamental layout component with polymorphic `as` prop
- **Flex**: Flexbox container with convenient props for direction, justify, align, gap, and wrap
- **Stack/HStack**: Vertical and horizontal stacks with consistent spacing
- **Grid/GridItem**: CSS Grid layouts with responsive column/row spanning
- **Container**: Responsive container with max-width constraints and padding
- **Divider**: Visual content separator with horizontal/vertical orientation and labels
- **Card**: Content container with header, title, description, content, and footer sub-components
- **Spacer**: Flexible spacing component for consistent gaps
- **Center**: Component for centering content horizontally and vertically

#### Typography Components
- **Text**: Text component with semantic variants (p, lead, small, muted, etc.)
- **Heading**: Semantic headings (h1-h6) with customizable visual sizes
- **Code**: Inline and block code display with syntax highlighting support
- **Link**: Styled links with variants and external link indicators

#### Form Components
- **Button**: Interactive buttons with variants (default, destructive, outline, secondary, ghost, link)
- **Input**: Text input with icon support and validation states
- **Textarea**: Multi-line text input with auto-resize option
- **Checkbox**: Checkbox input with labels and indeterminate state
- **Radio/RadioGroup**: Radio button inputs with group management
- **Label**: Form field labels with required indicators
- **FormField**: Complete form field wrapper with label, input, error, and helper text

#### Feedback Components
- **Alert**: Alert messages with variants (default, success, warning, destructive, info)
- **Badge**: Status and label badges with multiple variants and sizes
- **Spinner**: Loading spinners with customizable size, color, and speed
- **Progress**: Progress bars with determinate and indeterminate states
- **Skeleton**: Loading placeholders for text and content areas

#### Utilities
- **Class Variance Authority**: Component variant system for consistent styling
- **CSS Custom Properties**: Automatic generation of CSS variables from design tokens
- **Responsive Design**: Mobile-first responsive design patterns
- **Accessibility**: ARIA attributes and keyboard navigation support

#### Build System
- **Vite**: Modern build system with HMR and optimized production builds
- **ESM/UMD**: Dual package format support for modern and legacy environments
- **TypeScript Declarations**: Comprehensive type definitions for all components
- **CSS Extraction**: Separate CSS bundle for styling

### Developer Experience
- **Demo Application**: Interactive demo showcasing all components
- **Type Safety**: Comprehensive TypeScript interfaces and prop validation
- **Tree Shaking**: Optimized bundle size with dead code elimination
- **Documentation**: Comprehensive README with usage examples
