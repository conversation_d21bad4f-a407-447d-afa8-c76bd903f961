{"name": "@augment/demo", "version": "0.1.0", "description": "Demo application for Augment UI component library", "type": "module", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "typecheck": "tsc --noEmit", "lint": "echo \"No linting configured yet\"", "test": "echo \"No tests configured yet\""}, "dependencies": {"@augment/ui": "file:../ui", "react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "typescript": "^5.8.3", "vite": "^5.4.19"}}