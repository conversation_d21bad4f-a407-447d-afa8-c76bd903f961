import React from 'react'
import { createRoot } from 'react-dom/client'
import {
  ThemeProvider,
  Container,
  Stack,
  Heading,
  Text,
  Typography,
  Button,
  Input,
  FormField,
  Checkbox,
  Radio,
  RadioGroup,
  Flex,
  Box,
  Grid,
  GridItem,
  Code,
  Link,
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
  CardFooter,
  Divider,
  Alert,
  AlertTitle,
  AlertDescription,
  Badge,
  Spinner,
  Progress,
  Skeleton,
  SkeletonText,
} from '@augment/ui'
import '@augment/ui/styles'

const Demo: React.FC = () => {
  const [inputValue, setInputValue] = React.useState('')
  const [checkboxValue, setCheckboxValue] = React.useState(false)
  const [radioValue, setRadioValue] = React.useState('option1')

  return (
    <ThemeProvider>
      <Container maxWidth="4xl" padding={8}>
        <Stack spacing={8}>
          <Box>
            <Heading as="h1" size="h1">
              Augment UI Component Library
            </Heading>
            <Text size="lg" color="muted">
              A flexible React component library with design tokens, Tailwind CSS, and TypeScript
            </Text>
          </Box>

          {/* Typography Section */}
          <Box>
            <Heading as="h2" size="h2">Typography</Heading>
            <Stack spacing={4}>
              <Box>
                <Heading as="h3" size="h3">Headings</Heading>
                <Stack spacing={2}>
                  <Heading as="h1" size="h1">Heading 1</Heading>
                  <Heading as="h2" size="h2">Heading 2</Heading>
                  <Heading as="h3" size="h3">Heading 3</Heading>
                  <Heading as="h4" size="h4">Heading 4</Heading>
                  <Heading as="h5" size="h5">Heading 5</Heading>
                  <Heading as="h6" size="h6">Heading 6</Heading>
                </Stack>
              </Box>

              <Box>
                <Heading as="h3" size="h3">Text Variants</Heading>
                <Stack spacing={2}>
                  <Text variant="lead">This is lead text</Text>
                  <Text variant="p">This is regular paragraph text</Text>
                  <Text variant="small">This is small text</Text>
                  <Text variant="muted">This is muted text</Text>
                  <Code>This is inline code</Code>
                  <Code block>
{`// This is a code block
function hello() {
  console.log('Hello, world!')
}`}
                  </Code>
                </Stack>
              </Box>

              <Box>
                <Heading as="h3" size="h3">Links</Heading>
                <Stack spacing={2}>
                  <Link href="#" variant="primary">Primary link</Link>
                  <Link href="#" variant="secondary">Secondary link</Link>
                  <Link href="#" variant="underline">Underlined link</Link>
                  <Link href="https://example.com" external>External link</Link>
                </Stack>
              </Box>

              <Box>
                <Heading as="h3" size="h3">Typography Component</Heading>
                <Stack spacing={3}>
                  <Box>
                    <Text weight="medium" className="mb-2">Display Variants</Text>
                    <Stack spacing={2}>
                      <Typography variant="h1">Heading 1 - Main Title</Typography>
                      <Typography variant="h2">Heading 2 - Section Title</Typography>
                      <Typography variant="h3">Heading 3 - Subsection</Typography>
                      <Typography variant="h4">Heading 4 - Minor Heading</Typography>
                      <Typography variant="h5">Heading 5 - Small Heading</Typography>
                      <Typography variant="h6">Heading 6 - Smallest Heading</Typography>
                    </Stack>
                  </Box>

                  <Box>
                    <Text weight="medium" className="mb-2">Body Text</Text>
                    <Stack spacing={2}>
                      <Typography variant="body1">
                        Body 1 - This is the default body text variant with comfortable line height for reading.
                      </Typography>
                      <Typography variant="body2">
                        Body 2 - This is a smaller body text variant, useful for secondary content.
                      </Typography>
                    </Stack>
                  </Box>

                  <Box>
                    <Text weight="medium" className="mb-2">Subtitles</Text>
                    <Stack spacing={2}>
                      <Typography variant="subtitle1">Subtitle 1 - Medium emphasis subtitle</Typography>
                      <Typography variant="subtitle2">Subtitle 2 - Smaller subtitle for less emphasis</Typography>
                    </Stack>
                  </Box>

                  <Box>
                    <Text weight="medium" className="mb-2">Special Variants</Text>
                    <Stack spacing={2}>
                      <Typography variant="caption">Caption - Small text for image captions or footnotes</Typography>
                      <Typography variant="overline">Overline - Uppercase text for categories</Typography>
                      <Typography variant="button">Button - Text styled for buttons</Typography>
                    </Stack>
                  </Box>

                  <Box>
                    <Text weight="medium" className="mb-2">Colors</Text>
                    <Stack spacing={2}>
                      <Typography variant="body1" color="primary">Primary colored text</Typography>
                      <Typography variant="body1" color="secondary">Secondary colored text</Typography>
                      <Typography variant="body1" color="success">Success colored text</Typography>
                      <Typography variant="body1" color="warning">Warning colored text</Typography>
                      <Typography variant="body1" color="error">Error colored text</Typography>
                      <Typography variant="body1" color="textSecondary">Secondary text color</Typography>
                    </Stack>
                  </Box>

                  <Box>
                    <Text weight="medium" className="mb-2">Alignment</Text>
                    <Stack spacing={2}>
                      <Typography variant="body1" align="left">Left aligned text</Typography>
                      <Typography variant="body1" align="center">Center aligned text</Typography>
                      <Typography variant="body1" align="right">Right aligned text</Typography>
                    </Stack>
                  </Box>

                  <Box>
                    <Text weight="medium" className="mb-2">Custom Component</Text>
                    <Stack spacing={2}>
                      <Typography variant="h4" component="div">
                        This is h4 styling but rendered as a div element
                      </Typography>
                      <Typography variant="body1" component="span" gutterBottom>
                        This is body text rendered as a span with bottom margin
                      </Typography>
                      <Typography variant="body2" noWrap className="w-48 border p-2">
                        This text will be truncated with ellipsis when it overflows the container width
                      </Typography>
                    </Stack>
                  </Box>
                </Stack>
              </Box>
            </Stack>
          </Box>

          {/* Layout Section */}
          <Box>
            <Heading as="h2" size="h2">Layout</Heading>
            <Stack spacing={4}>
              <Box>
                <Heading as="h3" size="h3">Flex Layout</Heading>
                <Flex gap={4} justify="between" align="center" className="p-4 bg-neutral-100 rounded">
                  <Text>Item 1</Text>
                  <Text>Item 2</Text>
                  <Text>Item 3</Text>
                </Flex>
              </Box>

              <Box>
                <Heading as="h3" size="h3">Grid Layout</Heading>
                <Grid columns={3} gap={4}>
                  <GridItem className="p-4 bg-primary-100 rounded">
                    <Text>Grid Item 1</Text>
                  </GridItem>
                  <GridItem className="p-4 bg-primary-100 rounded">
                    <Text>Grid Item 2</Text>
                  </GridItem>
                  <GridItem className="p-4 bg-primary-100 rounded">
                    <Text>Grid Item 3</Text>
                  </GridItem>
                </Grid>
              </Box>
            </Stack>
          </Box>

          {/* Feedback Section */}
          <Box>
            <Heading as="h2" size="h2">Feedback Components</Heading>
            <Stack spacing={6}>
              <Box>
                <Heading as="h3" size="h3">Alerts</Heading>
                <Stack spacing={4}>
                  <Alert variant="default">
                    <AlertTitle>Default Alert</AlertTitle>
                    <AlertDescription>This is a default alert message.</AlertDescription>
                  </Alert>
                  <Alert variant="success">
                    <AlertTitle>Success!</AlertTitle>
                    <AlertDescription>Your action was completed successfully.</AlertDescription>
                  </Alert>
                  <Alert variant="warning">
                    <AlertTitle>Warning</AlertTitle>
                    <AlertDescription>Please review your input before proceeding.</AlertDescription>
                  </Alert>
                  <Alert variant="destructive" dismissible>
                    <AlertTitle>Error</AlertTitle>
                    <AlertDescription>Something went wrong. Please try again.</AlertDescription>
                  </Alert>
                </Stack>
              </Box>

              <Box>
                <Heading as="h3" size="h3">Badges</Heading>
                <Flex gap={2} wrap="wrap">
                  <Badge variant="default">Default</Badge>
                  <Badge variant="secondary">Secondary</Badge>
                  <Badge variant="success">Success</Badge>
                  <Badge variant="warning">Warning</Badge>
                  <Badge variant="destructive">Error</Badge>
                  <Badge variant="outline">Outline</Badge>
                </Flex>
              </Box>

              <Box>
                <Heading as="h3" size="h3">Loading States</Heading>
                <Stack spacing={4}>
                  <Box>
                    <Text weight="medium" className="mb-2">Spinners</Text>
                    <Flex gap={4} align="center">
                      <Spinner size="xs" />
                      <Spinner size="sm" />
                      <Spinner size="md" />
                      <Spinner size="lg" />
                      <Spinner size="xl" />
                    </Flex>
                  </Box>

                  <Box>
                    <Text weight="medium" className="mb-2">Progress</Text>
                    <Stack spacing={3}>
                      <Progress value={25} showValue label="Upload Progress" />
                      <Progress value={60} color="success" showValue />
                      <Progress value={85} color="warning" />
                      <Progress indeterminate label="Processing..." />
                    </Stack>
                  </Box>

                  <Box>
                    <Text weight="medium" className="mb-2">Skeleton Loading</Text>
                    <Card>
                      <CardHeader>
                        <Flex gap={4}>
                          <Skeleton variant="circular" width={40} height={40} />
                          <Stack spacing={2}>
                            <Skeleton variant="text" width="150px" />
                            <Skeleton variant="text" width="100px" />
                          </Stack>
                        </Flex>
                      </CardHeader>
                      <CardContent>
                        <SkeletonText lines={3} />
                      </CardContent>
                    </Card>
                  </Box>
                </Stack>
              </Box>
            </Stack>
          </Box>

          <Divider />

          {/* Cards Section */}
          <Box>
            <Heading as="h2" size="h2">Cards</Heading>
            <Grid columns={2} gap={6}>
              <Card>
                <CardHeader>
                  <CardTitle>Card Title</CardTitle>
                  <CardDescription>
                    This is a description of the card content.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Text>This is the main content of the card.</Text>
                </CardContent>
                <CardFooter>
                  <Button size="sm">Action</Button>
                </CardFooter>
              </Card>

              <Card variant="elevated">
                <CardHeader>
                  <CardTitle>Elevated Card</CardTitle>
                  <CardDescription>
                    This card has an elevated shadow.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Text>Content with elevated styling.</Text>
                </CardContent>
              </Card>
            </Grid>
          </Box>

          <Divider />

          {/* Form Section */}
          <Box>
            <Heading as="h2" size="h2">Form Components</Heading>
            <Stack spacing={6}>
              <Box>
                <Heading as="h3" size="h3">Buttons</Heading>
                <Flex gap={4} wrap="wrap">
                  <Button variant="default">Default</Button>
                  <Button variant="destructive">Destructive</Button>
                  <Button variant="outline">Outline</Button>
                  <Button variant="secondary">Secondary</Button>
                  <Button variant="ghost">Ghost</Button>
                  <Button variant="link">Link</Button>
                </Flex>
              </Box>

              <Box>
                <Heading as="h3" size="h3">Button Sizes</Heading>
                <Flex gap={4} align="center" wrap="wrap">
                  <Button size="xs">Extra Small</Button>
                  <Button size="sm">Small</Button>
                  <Button size="md">Medium</Button>
                  <Button size="lg">Large</Button>
                  <Button size="xl">Extra Large</Button>
                </Flex>
              </Box>

              <Box>
                <Heading as="h3" size="h3">Form Fields</Heading>
                <Stack spacing={4}>
                  <FormField
                    label="Email"
                    required
                    helperText="Enter your email address"
                  >
                    <Input
                      type="email"
                      placeholder="<EMAIL>"
                      value={inputValue}
                      onChange={(e) => setInputValue(e.target.value)}
                    />
                  </FormField>

                  <FormField
                    label="Password"
                    required
                    error={inputValue.length > 0 && inputValue.length < 8 ? "Password must be at least 8 characters" : undefined}
                  >
                    <Input
                      type="password"
                      placeholder="Enter your password"
                    />
                  </FormField>

                  <Checkbox
                    label="I agree to the terms and conditions"
                    checked={checkboxValue}
                    onChange={(e) => setCheckboxValue(e.target.checked)}
                  />

                  <Box>
                    <Text weight="medium" className="mb-3">Choose an option:</Text>
                    <RadioGroup
                      name="demo-radio"
                      value={radioValue}
                      onChange={setRadioValue}
                    >
                      <Radio value="option1" label="Option 1" />
                      <Radio value="option2" label="Option 2" />
                      <Radio value="option3" label="Option 3" />
                    </RadioGroup>
                  </Box>

                  <Button type="submit">Submit Form</Button>
                </Stack>
              </Box>
            </Stack>
          </Box>
        </Stack>
      </Container>
    </ThemeProvider>
  )
}

// Mount the demo
const container = document.getElementById('root')
if (container) {
  const root = createRoot(container)
  root.render(<Demo />)
}
