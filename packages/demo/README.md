# @augment/demo

Demo application showcasing the Augment UI component library.

## Overview

This demo application demonstrates all the components available in the `@augment/ui` library, including:

- Layout components (Box, <PERSON>lex, <PERSON>ack, Grid, etc.)
- Typography components (Text, Heading, Code, Link)
- Form components (Button, Input, Checkbox, Radio, etc.)
- Feedback components (Alert, Badge, Spinner, Progress, Skeleton)

## Development

```bash
# From the root directory
npm run dev

# Or run directly from this package
npm run dev --workspace=@augment/demo
```

## Building

```bash
# From the root directory
npm run build:demo

# Or run directly from this package
npm run build --workspace=@augment/demo
```

## Features Demonstrated

- **Theme System**: Shows how to use the ThemeProvider and design tokens
- **Component Variants**: Demonstrates different variants and sizes for each component
- **Form Handling**: Examples of form validation and state management
- **Layout Patterns**: Common layout patterns using the layout components
- **Responsive Design**: Mobile-first responsive design examples
- **Accessibility**: Proper ARIA attributes and keyboard navigation

## Structure

- `src/demo.tsx` - Main demo application component
- `index.html` - HTML entry point
- `vite.config.ts` - Vite configuration for development and building

This demo serves as both documentation and testing for the component library.
