import React from 'react'
import { MarkdownRenderer, CustomComponent } from './src/index'

// Example custom components
const CustomButton = ({ children, variant = 'primary', ...props }: any) => (
  <button 
    className={`btn btn-${variant} px-4 py-2 rounded`} 
    {...props}
  >
    {children}
  </button>
)

const Alert = ({ type = 'info', children }: any) => (
  <div className={`alert alert-${type} p-4 mb-4 rounded border-l-4`}>
    {children}
  </div>
)

const CodeBlock = ({ language, children }: any) => (
  <pre className={`language-${language} bg-gray-100 p-4 rounded overflow-x-auto`}>
    <code>{children}</code>
  </pre>
)

// Define custom components
const customComponents: CustomComponent[] = [
  {
    name: 'button',
    component: CustomButton,
    hasChildren: true,
    defaultProps: { variant: 'primary' }
  },
  {
    name: 'alert',
    component: Alert,
    hasChildren: true,
    defaultProps: { type: 'info' }
  },
  {
    name: 'code-block',
    component: CodeBlock,
    hasChildren: true
  }
]

// Example markdown content
const markdownContent = `
# Markdown Renderer Example

This is a demonstration of the **@augment/markdown-renderer** package.

## Features

- ✅ Standard markdown rendering
- ✅ Custom React components
- ✅ TypeScript support
- ✅ Configurable options

## Custom Components

You can use custom React components in your markdown:

<alert type="success">
This is a **success** alert with markdown content!
</alert>

<alert type="warning">
⚠️ This is a warning alert
</alert>

<button variant="secondary">Click me!</button>

<button variant="primary">Primary Button</button>

## Code Example

Here's how to use it:

\`\`\`tsx
import { MarkdownRenderer } from '@augment/markdown-renderer'

function App() {
  return (
    <MarkdownRenderer 
      content={markdownContent}
      components={customComponents}
    />
  )
}
\`\`\`

<code-block language="javascript">
const example = "This is a custom code block component";
console.log(example);
</code-block>

## Lists

- Item 1
- Item 2
  - Nested item
  - Another nested item
- Item 3

1. First ordered item
2. Second ordered item
3. Third ordered item

## Links and Emphasis

Visit [Augment Code](https://augmentcode.com) for more information.

This text has *italic* and **bold** formatting.

---

That's it! The markdown renderer supports both standard markdown and custom React components.
`

// Example component usage
export function MarkdownExample() {
  return (
    <div className="max-w-4xl mx-auto p-6">
      <MarkdownRenderer 
        content={markdownContent}
        components={customComponents}
        className="prose prose-lg"
      />
    </div>
  )
}

export default MarkdownExample
