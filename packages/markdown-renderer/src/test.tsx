import React from 'react'
import { Markdown<PERSON>ender<PERSON>, useM<PERSON>down<PERSON><PERSON><PERSON>, CustomComponent } from './index'

// Simple test components
const TestButton = ({ children, ...props }: any) => (
  <button data-testid="custom-button" {...props}>
    {children}
  </button>
)

const TestAlert = ({ type = 'info', children }: any) => (
  <div data-testid="custom-alert" data-type={type}>
    {children}
  </div>
)

// Test custom components
const testComponents: CustomComponent[] = [
  {
    name: 'button',
    component: TestButton,
    hasChildren: true
  },
  {
    name: 'alert',
    component: TestAlert,
    hasChildren: true,
    defaultProps: { type: 'info' }
  }
]

// Test cases
export function TestBasicMarkdown() {
  const markdown = `
# Test Heading

This is a **bold** text and *italic* text.

- List item 1
- List item 2
`

  return (
    <div data-testid="basic-markdown">
      <MarkdownRenderer content={markdown} />
    </div>
  )
}

export function TestCustomComponents() {
  const markdown = `
# Custom Components Test

<button>Test Button</button>

<alert type="warning">Test Alert</alert>
`

  return (
    <div data-testid="custom-components">
      <MarkdownRenderer 
        content={markdown} 
        components={testComponents}
      />
    </div>
  )
}

export function TestHook() {
  const markdown = `## Hook Test\n\nThis uses the **useMarkdownRenderer** hook.`
  const renderedContent = useMarkdownRenderer(markdown)

  return (
    <div data-testid="hook-test">
      {renderedContent}
    </div>
  )
}

export function TestEmptyContent() {
  return (
    <div data-testid="empty-content">
      <MarkdownRenderer content="" />
    </div>
  )
}

export function TestWithClassName() {
  const markdown = `# Styled Content`
  
  return (
    <div data-testid="with-classname">
      <MarkdownRenderer 
        content={markdown} 
        className="test-class"
      />
    </div>
  )
}

// Manual test runner (for development)
export function TestRunner() {
  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
      <h1>Markdown Renderer Tests</h1>
      
      <section style={{ marginBottom: '30px' }}>
        <h2>Basic Markdown</h2>
        <TestBasicMarkdown />
      </section>

      <section style={{ marginBottom: '30px' }}>
        <h2>Custom Components</h2>
        <TestCustomComponents />
      </section>

      <section style={{ marginBottom: '30px' }}>
        <h2>Hook Usage</h2>
        <TestHook />
      </section>

      <section style={{ marginBottom: '30px' }}>
        <h2>Empty Content</h2>
        <TestEmptyContent />
      </section>

      <section style={{ marginBottom: '30px' }}>
        <h2>With ClassName</h2>
        <TestWithClassName />
      </section>
    </div>
  )
}
