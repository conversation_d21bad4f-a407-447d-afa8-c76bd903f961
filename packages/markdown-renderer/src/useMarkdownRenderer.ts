import { useMemo } from 'react'
import MarkdownIt from 'markdown-it'
import type { Options } from 'markdown-it'
import { CustomComponent } from './types'
import { convertTokens, renderTokens } from './renderer'

/**
 * Hook for rendering markdown content with custom components
 */
export function useMarkdownRenderer(
  content: string,
  components: CustomComponent[] = [],
  markdownOptions: Options = {}
) {
  // Create markdown-it instance
  const md = useMemo(() => {
    return new MarkdownIt({
      html: true,
      linkify: true,
      typographer: true,
      breaks: false,
      ...markdownOptions
    })
  }, [markdownOptions])

  // Create components map
  const componentsMap = useMemo(() => {
    const map = new Map()
    components.forEach(comp => {
      map.set(comp.name, comp)
    })
    return map
  }, [components])

  // Parse and render markdown
  const renderedContent = useMemo(() => {
    if (!content.trim()) {
      return []
    }

    try {
      const tokens = md.parse(content, {})
      const convertedTokens = convertTokens(tokens)
      return renderTokens(convertedTokens, componentsMap)
    } catch (error) {
      console.error('Error rendering markdown:', error)
      return []
    }
  }, [content, md, componentsMap])

  return renderedContent
}
