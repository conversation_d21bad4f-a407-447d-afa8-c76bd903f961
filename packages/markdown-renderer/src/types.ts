import { ComponentType, ReactNode } from 'react'
import type { Options } from 'markdown-it'

/**
 * Custom React component that can be used in markdown rendering
 */
export interface CustomComponent {
  /** The name/tag to match in markdown */
  name: string
  /** The React component to render */
  component: ComponentType<any>
  /** Whether this component should render children */
  hasChildren?: boolean
  /** Custom props to pass to the component */
  defaultProps?: Record<string, any>
}

/**
 * Props for the MarkdownRenderer component
 */
export interface MarkdownRendererProps {
  /** The markdown content to render */
  content: string
  /** Array of custom React components to use in rendering */
  components?: CustomComponent[]
  /** Additional markdown-it options */
  markdownOptions?: Options
  /** Additional markdown-it plugins */
  plugins?: Array<{
    plugin: any
    options?: any
  }>
  /** Custom className for the wrapper element */
  className?: string
  /** Custom wrapper component (defaults to div) */
  wrapper?: ComponentType<any> | string
}

/**
 * Token data structure for custom rendering
 */
export interface TokenData {
  type: string
  tag: string
  attrs: Array<[string, string]>
  content: string
  children?: TokenData[]
  level: number
  block: boolean
}

/**
 * Custom renderer function type
 */
export type CustomRenderer = (
  tokens: TokenData[],
  components: Map<string, CustomComponent>
) => ReactNode[]
