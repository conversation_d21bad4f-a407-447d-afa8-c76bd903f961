import { ReactNode, createElement } from 'react'
import { TokenData, CustomComponent } from './types'

/**
 * Converts markdown-it tokens to our TokenData format
 */
export function convertTokens(tokens: any[]): TokenData[] {
  return tokens.map(token => ({
    type: token.type,
    tag: token.tag,
    attrs: token.attrs || [],
    content: token.content || '',
    children: token.children ? convertTokens(token.children) : undefined,
    level: token.level || 0,
    block: token.block || false
  }))
}

/**
 * Gets attributes as an object from token attrs array
 */
function getAttrsObject(attrs: Array<[string, string]>): Record<string, string> {
  const result: Record<string, string> = {}
  attrs.forEach(([key, value]) => {
    result[key] = value
  })
  return result
}

/**
 * Renders tokens to React elements using custom components
 */
export function renderTokens(
  tokens: TokenData[],
  components: Map<string, CustomComponent>,
  key = 0
): ReactNode[] {
  const result: ReactNode[] = []
  let currentKey = key

  for (let i = 0; i < tokens.length; i++) {
    const token = tokens[i]
    const elementKey = `token-${currentKey++}`

    // Handle text content
    if (token.type === 'text' || token.type === 'code_inline') {
      if (token.content) {
        result.push(
          token.type === 'code_inline' 
            ? createElement('code', { key: elementKey }, token.content)
            : token.content
        )
      }
      continue
    }

    // Handle opening tags
    if (token.type.endsWith('_open')) {
      const tagName = token.tag
      const customComponent = components.get(tagName)
      
      if (customComponent) {
        // Use custom component
        const attrs = getAttrsObject(token.attrs)
        const props = {
          ...customComponent.defaultProps,
          ...attrs,
          key: elementKey
        }

        // Find matching closing token and collect children
        let children: ReactNode[] = []
        if (customComponent.hasChildren !== false) {
          const closingType = token.type.replace('_open', '_close')
          let level = 1
          let j = i + 1
          const childTokens: TokenData[] = []

          while (j < tokens.length && level > 0) {
            const nextToken = tokens[j]
            if (nextToken.type === token.type) {
              level++
            } else if (nextToken.type === closingType) {
              level--
            }
            
            if (level > 0) {
              childTokens.push(nextToken)
            }
            j++
          }

          children = renderTokens(childTokens, components, currentKey)
          currentKey += childTokens.length
          i = j - 1 // Skip to after closing token
        }

        result.push(
          createElement(customComponent.component, props, ...children)
        )
      } else {
        // Use standard HTML element
        const attrs = getAttrsObject(token.attrs)
        const props = { ...attrs, key: elementKey }

        // Find matching closing token and collect children
        const closingType = token.type.replace('_open', '_close')
        let level = 1
        let j = i + 1
        const childTokens: TokenData[] = []

        while (j < tokens.length && level > 0) {
          const nextToken = tokens[j]
          if (nextToken.type === token.type) {
            level++
          } else if (nextToken.type === closingType) {
            level--
          }
          
          if (level > 0) {
            childTokens.push(nextToken)
          }
          j++
        }

        const children = renderTokens(childTokens, components, currentKey)
        currentKey += childTokens.length
        i = j - 1 // Skip to after closing token

        result.push(createElement(tagName, props, ...children))
      }
    }
    // Handle self-closing tags
    else if (token.type.endsWith('_self')) {
      const tagName = token.tag
      const customComponent = components.get(tagName)
      
      if (customComponent) {
        const attrs = getAttrsObject(token.attrs)
        const props = {
          ...customComponent.defaultProps,
          ...attrs,
          key: elementKey
        }
        result.push(createElement(customComponent.component, props))
      } else {
        const attrs = getAttrsObject(token.attrs)
        const props = { ...attrs, key: elementKey }
        result.push(createElement(tagName, props))
      }
    }
    // Handle inline tokens
    else if (token.type === 'inline' && token.children) {
      result.push(...renderTokens(token.children, components, currentKey))
      currentKey += token.children.length
    }
    // Handle other block tokens
    else if (token.content) {
      result.push(token.content)
    }
  }

  return result
}
