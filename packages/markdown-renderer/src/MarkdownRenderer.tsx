import React, { useMemo } from 'react'
import MarkdownIt from 'markdown-it'
import type { Options } from 'markdown-it'
import { MarkdownRendererProps } from './types'
import { convertTokens, renderTokens } from './renderer'

/**
 * Default markdown-it options
 */
const DEFAULT_MARKDOWN_OPTIONS: Options = {
  html: true,
  linkify: true,
  typographer: true,
  breaks: false
}

/**
 * MarkdownRenderer component that renders markdown content to React elements
 * with support for custom React components
 */
export const MarkdownRenderer: React.FC<MarkdownRendererProps> = ({
  content,
  components = [],
  markdownOptions = {},
  plugins = [],
  className,
  wrapper: Wrapper = 'div'
}) => {
  // Create markdown-it instance with options and plugins
  const md = useMemo(() => {
    const markdownIt = new MarkdownIt({
      ...DEFAULT_MARKDOWN_OPTIONS,
      ...markdownOptions
    })

    // Apply plugins
    plugins.forEach(({ plugin, options }) => {
      markdownIt.use(plugin, options)
    })

    return markdownIt
  }, [markdownOptions, plugins])

  // Create components map for efficient lookup
  const componentsMap = useMemo(() => {
    const map = new Map()
    components.forEach(comp => {
      map.set(comp.name, comp)
    })
    return map
  }, [components])

  // Parse and render markdown
  const renderedContent = useMemo(() => {
    if (!content.trim()) {
      return []
    }

    try {
      // Parse markdown to tokens
      const tokens = md.parse(content, {})
      
      // Convert to our token format
      const convertedTokens = convertTokens(tokens)
      
      // Render to React elements
      return renderTokens(convertedTokens, componentsMap)
    } catch (error) {
      console.error('Error rendering markdown:', error)
      return [React.createElement('div', { key: 'error' }, 'Error rendering markdown content')]
    }
  }, [content, md, componentsMap])

  return React.createElement(
    Wrapper,
    { className },
    ...renderedContent
  )
}

export default MarkdownRenderer
