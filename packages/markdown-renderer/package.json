{"name": "@augment/markdown-renderer", "version": "0.1.0", "description": "A React markdown renderer using markdown-it with custom component support", "type": "module", "main": "./dist/markdown-renderer.umd.js", "module": "./dist/markdown-renderer.es.js", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/markdown-renderer.es.js", "require": "./dist/markdown-renderer.umd.js"}}, "files": ["dist"], "scripts": {"dev": "vite build --watch", "build": "npm run build:types && vite build", "build:types": "tsc -p tsconfig.build.json", "clean": "rm -rf dist", "typecheck": "tsc --noEmit", "lint": "echo \"No linting configured yet\"", "test": "echo \"No tests configured yet\""}, "keywords": ["react", "markdown", "markdown-it", "renderer", "typescript", "components"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dependencies": {"markdown-it": "^14.1.0"}, "devDependencies": {"@types/markdown-it": "^14.1.2", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "react": "^19.1.0", "react-dom": "^19.1.0", "rollup-plugin-dts": "^6.2.1", "typescript": "^5.8.3", "vite": "^5.4.19"}}