# @augment/markdown-renderer

A React markdown renderer using markdown-it with custom component support. This package allows you to render markdown content to React components while providing the ability to replace standard HTML elements with custom React components.

## Features

- 🚀 Built with TypeScript for full type safety
- 📝 Uses markdown-it for robust markdown parsing
- 🎨 Support for custom React components
- 🔧 Configurable markdown-it options and plugins
- 🪝 Includes both component and hook APIs
- ⚡ Optimized with memoization for performance

## Installation

```bash
npm install @augment/markdown-renderer
```

## Basic Usage

### Using the Component

```tsx
import { MarkdownRenderer } from '@augment/markdown-renderer'

function App() {
  const markdown = `
# Hello World

This is **bold** text and this is *italic* text.

- List item 1
- List item 2
- List item 3
`

  return (
    <MarkdownRenderer 
      content={markdown}
      className="prose"
    />
  )
}
```

### Using the Hook

```tsx
import { useMarkdownRenderer } from '@augment/markdown-renderer'

function App() {
  const markdown = `# Hello World\n\nThis is a paragraph.`
  const renderedContent = useMarkdownRenderer(markdown)

  return (
    <div className="prose">
      {renderedContent}
    </div>
  )
}
```

## Custom Components

You can replace standard HTML elements with custom React components:

```tsx
import { MarkdownRenderer, CustomComponent } from '@augment/markdown-renderer'

// Custom button component
const CustomButton = ({ children, ...props }) => (
  <button className="btn btn-primary" {...props}>
    {children}
  </button>
)

// Custom alert component
const Alert = ({ type = 'info', children }) => (
  <div className={`alert alert-${type}`}>
    {children}
  </div>
)

const customComponents: CustomComponent[] = [
  {
    name: 'button',
    component: CustomButton,
    hasChildren: true,
    defaultProps: { type: 'button' }
  },
  {
    name: 'alert',
    component: Alert,
    hasChildren: true,
    defaultProps: { type: 'info' }
  }
]

function App() {
  const markdown = `
# Custom Components Example

<button>Click me!</button>

<alert type="warning">
This is a warning alert with **markdown** content!
</alert>
`

  return (
    <MarkdownRenderer 
      content={markdown}
      components={customComponents}
    />
  )
}
```

## Advanced Configuration

### Markdown-it Options

```tsx
import { MarkdownRenderer } from '@augment/markdown-renderer'

const markdownOptions = {
  html: true,
  linkify: true,
  typographer: true,
  breaks: true
}

function App() {
  return (
    <MarkdownRenderer 
      content={markdown}
      markdownOptions={markdownOptions}
    />
  )
}
```

### Using Plugins

```tsx
import { MarkdownRenderer } from '@augment/markdown-renderer'
import markdownItAnchor from 'markdown-it-anchor'
import markdownItToc from 'markdown-it-toc-done-right'

const plugins = [
  { plugin: markdownItAnchor, options: { permalink: true } },
  { plugin: markdownItToc }
]

function App() {
  return (
    <MarkdownRenderer 
      content={markdown}
      plugins={plugins}
    />
  )
}
```

### Custom Wrapper

```tsx
import { MarkdownRenderer } from '@augment/markdown-renderer'

const CustomWrapper = ({ children, className }) => (
  <article className={`markdown-content ${className}`}>
    {children}
  </article>
)

function App() {
  return (
    <MarkdownRenderer 
      content={markdown}
      wrapper={CustomWrapper}
      className="prose"
    />
  )
}
```

## API Reference

### MarkdownRenderer Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `content` | `string` | - | The markdown content to render |
| `components` | `CustomComponent[]` | `[]` | Array of custom React components |
| `markdownOptions` | `MarkdownIt.Options` | `{}` | Additional markdown-it options |
| `plugins` | `Array<{plugin: any, options?: any}>` | `[]` | Markdown-it plugins to use |
| `className` | `string` | - | CSS class for the wrapper element |
| `wrapper` | `ComponentType` | `'div'` | Custom wrapper component |

### CustomComponent Interface

```tsx
interface CustomComponent {
  name: string                    // HTML tag name to replace
  component: ComponentType<any>   // React component to use
  hasChildren?: boolean          // Whether component accepts children
  defaultProps?: Record<string, any> // Default props to pass
}
```

## License

MIT
