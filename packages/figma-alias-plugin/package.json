{"name": "@augment/figma-alias-plugin", "version": "0.1.0", "description": "Figma plugin to create variable aliases between libraries and collections", "type": "module", "private": true, "watch": {"build:ui": {"patterns": ["src"], "extensions": "js,jsx,ts,tsx,html,css"}, "build:plugin": {"patterns": ["src"], "extensions": "js,jsx,ts,tsx,html,css"}}, "scripts": {"dev": "concurrently \"npm run watch-ui\" \"npm run watch-plugin\"", "watch-ui": "npm-watch build:ui", "watch-plugin": "npm-watch build:plugin", "build": "npm run build:ui && npm run build:plugin", "build:ui": "vite build -c ./vite.config.ui.ts", "build:plugin": "vite build -c ./vite.config.plugin.ts", "preview": "vite preview", "typecheck": "tsc --noEmit", "lint": "echo \"No linting configured yet\"", "test": "echo \"No tests configured yet\""}, "dependencies": {"react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@figma/plugin-typings": "^1.98.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "concurrently": "^9.2.0", "npm-watch": "^0.13.0", "typescript": "^5.8.3", "vite": "^5.4.19", "vite-plugin-generate-file": "^0.3.1", "vite-plugin-singlefile": "^2.2.0"}}