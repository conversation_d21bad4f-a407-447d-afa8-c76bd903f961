// https://docs.tokens.studio/debug/remap-tokens
// https://github.com/destefanis/figma-find-variables/blob/main/src/plugin/controller.ts
// for (const node of nodes) {
  // const boundVariables = node.boundVariables;
  // Object.keys(boundVariables).forEach(async (key) => {
    // const variableObject = boundVariables[key];
    // const variable = figma.variables.getVariableById(variableObject[0].id);
    // console.log('variable', variable.name);
  // })
// }
export default {
  name: "Augment UI - Variable Alias Manager",
  id: "augment-ui-variable-alias-manager",
  api: "1.0.0",
  main: "./plugin.js",
  ui: "./ui.html",
  editorType: ["figma"],
  enableProposedApi: false,
  permissions: ["teamlibrary"],
  networkAccess: {
    allowedDomains: ["none"],
  },
};
