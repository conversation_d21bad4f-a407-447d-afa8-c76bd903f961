# Augment UI - Variable Alias Manager

A Figma plugin that enables creating and managing variable aliases between different collections and libraries. This plugin allows you to create references from one variable to another, enabling powerful design system workflows.

## Features

- 🔗 **Create Variable Aliases**: Link variables to reference other variables' values
- 🎯 **Cross-Collection Support**: Create aliases between different variable collections
- 📚 **Library Integration**: Support for aliases between local and remote libraries
- 🔍 **Advanced Filtering**: Filter variables by type, collection, source, and alias status
- ✅ **Smart Validation**: Prevents circular references and type mismatches
- 📊 **Alias Management**: View, organize, and remove existing aliases
- 🎨 **Mode Support**: Create aliases for specific variable modes

## Use Cases

### Design System Consistency
- Create semantic tokens that reference base tokens
- Maintain consistency across different component libraries
- Enable theme switching through variable aliases

### Cross-Library References
- Reference design tokens from a central design system library
- Create local overrides that fall back to library defaults
- Maintain connections between brand and component libraries

### Conditional Design
- Create aliases that change based on different modes (light/dark, desktop/mobile)
- Build responsive design systems with mode-specific references
- Enable A/B testing through variable switching

## How It Works

### Variable Aliases in Figma
When you create an alias, the source variable will reference the target variable's value. This means:
- Changes to the target variable automatically update the source variable
- The source variable maintains its own identity while using another variable's value
- Aliases work across collections and libraries

### Alias Types Supported
- **Color → Color**: Reference color values
- **Number → Number**: Reference numeric values (spacing, sizing, etc.)
- **String → String**: Reference text values (font families, etc.)
- **Boolean → Boolean**: Reference boolean values

## Usage

### Creating Aliases

1. **Open the Plugin**: Go to Plugins → Augment UI - Variable Alias Manager
2. **Filter Variables**: Use the filter panel to find the variables you need
3. **Select Source**: Choose the variable that will reference another variable
4. **Select Target**: Choose the variable to reference
5. **Choose Mode**: Select which mode to create the alias for
6. **Create Alias**: Click "Create Alias" to establish the connection

### Managing Aliases

1. **Switch to Manage Tab**: Click "Manage Aliases" to view existing aliases
2. **Filter Aliases**: Search and filter to find specific aliases
3. **Remove Aliases**: Click the × button to remove unwanted aliases
4. **Sort Aliases**: Sort by name, collection, or creation date

## Interface

### Create Aliases Tab
- **Filter Panel**: Filter variables by type, collection, source, and status
- **Variable Selection**: Side-by-side selection of source and target variables
- **Mode Selection**: Choose which variable mode to create the alias for
- **Validation**: Real-time validation with error and warning messages
- **Options**: Configure alias creation behavior

### Manage Aliases Tab
- **Search & Filter**: Find specific aliases quickly
- **Alias List**: Visual representation of all variable connections
- **Bulk Actions**: Manage multiple aliases efficiently
- **Statistics**: Overview of alias usage across your design system

## Validation & Safety

### Automatic Validation
- **Type Checking**: Ensures source and target variables have compatible types
- **Circular Reference Prevention**: Prevents creating loops in variable references
- **Mode Compatibility**: Validates that the selected mode exists
- **Existing Alias Detection**: Warns when aliases already exist

### Error Prevention
- **Smart Filtering**: Only shows compatible variables for selection
- **Real-time Feedback**: Immediate validation as you make selections
- **Confirmation Dialogs**: Prevents accidental alias removal
- **Undo Support**: Changes can be undone through Figma's history

## Best Practices

### Naming Conventions
- Use clear, semantic names for aliased variables
- Follow a consistent naming pattern (e.g., `component/element/property`)
- Document the purpose of aliases in variable descriptions

### Organization
- Group related aliases in the same collection
- Use collections to separate different types of aliases (semantic, component, etc.)
- Keep alias chains shallow to maintain performance

### Maintenance
- Regularly review and clean up unused aliases
- Document alias relationships in your design system
- Test alias behavior across different modes and themes

## Technical Details

### Alias Implementation
- Uses Figma's native `VARIABLE_ALIAS` value type
- Maintains references through Figma's variable ID system
- Supports all variable scopes and modes
- Compatible with Figma's variable publishing system

### Performance Considerations
- Aliases are resolved at render time by Figma
- Deep alias chains may impact performance
- Plugin scans existing variables to detect current aliases
- Filtering and search are optimized for large variable sets

## Development

```bash
# Install dependencies
npm install

# Start development
npm run dev

# Build the plugin
npm run build
```

## Installation in Figma

1. Build the plugin: `npm run build`
2. In Figma, go to Plugins → Development → Import plugin from manifest
3. Select the `manifest.json` file from the `dist` folder
4. The plugin will appear in your Plugins menu

## Troubleshooting

### Common Issues

**Alias not working**: Ensure both variables have the same type and the target variable has a value for the selected mode.

**Can't find variables**: Check your filter settings and ensure variables are published if working with libraries.

**Circular reference error**: Review your alias chain to ensure no variable references itself directly or indirectly.

**Permission errors**: Ensure you have edit access to the variables you're trying to alias.

## License

MIT License - see LICENSE file for details.
