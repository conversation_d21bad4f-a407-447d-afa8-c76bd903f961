import { defineConfig } from 'vite'
import generateFile from "vite-plugin-generate-file";
import { viteSingleFile } from "vite-plugin-singlefile";
import figmaManifest from "./manifest";
import react from '@vitejs/plugin-react'
import { resolve } from 'path'

// import { defineConfig } from "vite";
// import path from "node:path";
// import { viteSingleFile } from "vite-plugin-singlefile";
// import react from "@vitejs/plugin-react";

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  plugins: [react(), viteSingleFile()],
  root: resolve("src"),
  build: {
    minify: mode === "production",
    cssMinify: mode === "production",
    // sourcemap: mode !== "production" ? "inline" : false,
    sourcemap: false,
    emptyOutDir: false,
    outDir: resolve("dist"),
    rollupOptions: {
      input: resolve("src/ui.html"),
    },
  },
}));

// export default defineConfig(({ mode }) => ({
  // build: {
    // outDir: resolve(__dirname, 'dist'),
    // emptyOutDir: true,
    // rollupOptions: {
      // input: {
        // ui: resolve(__dirname, 'src/ui.html'),
      // },
      // output: {
        // entryFileNames: '[name].js',
        // chunkFileNames: '[name].js',
        // assetFileNames: '[name].[ext]',
      // },
    // },
  // },
  // define: {
    // global: 'globalThis',
  // },
// }));
