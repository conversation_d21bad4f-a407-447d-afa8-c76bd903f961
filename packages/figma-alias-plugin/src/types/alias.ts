/**
 * Types for variable aliasing functionality
 */

// Variable information for display
export interface VariableInfo {
  id: string
  name: string
  key: string
  resolvedType: VariableResolvedDataType
  scopes: readonly VariableScope[]
  description: string
  collectionId: string
  collectionName: string
  remote: boolean
  hiddenFromPublishing: boolean
  modes: Array<{
    modeId: string
    name: string
    value: any
  }>
}

// Collection information
export interface CollectionInfo {
  id: string
  name: string
  description: string
  remote: boolean
  hiddenFromPublishing: boolean
  modes: Array<{
    modeId: string
    name: string
  }>
  variableIds: readonly string[]
  variableCount: number
}

// Library information
export interface LibraryInfo {
  key: string
  name: string
  description: string
  collections: CollectionInfo[]
}

// Alias relationship
export interface VariableAlias {
  id: string
  sourceVariableId: string
  sourceVariableName: string
  sourceCollectionName: string
  sourceLibraryName?: string
  targetVariableId: string
  targetVariableName: string
  targetCollectionName: string
  targetLibraryName?: string
  modeId: string
  modeName: string
  createdAt: string
}

// Alias creation request
export interface CreateAliasRequest {
  sourceVariableId: string
  targetVariableId: string
  modeId: string
  overwriteExisting?: boolean
}

// Alias operation result
export interface AliasOperationResult {
  success: boolean
  message: string
  aliasId?: string
  error?: string
}

// Plugin state
export interface PluginState {
  collections: CollectionInfo[]
  variables: VariableInfo[]
  aliases: VariableAlias[]
  selectedSourceCollection?: string
  selectedTargetCollection?: string
  selectedSourceVariable?: string
  selectedTargetVariable?: string
  selectedMode?: string
  isLoading: boolean
  error?: string
}

// Filter options
export interface FilterOptions {
  variableType?: VariableResolvedDataType
  collectionId?: string
  searchTerm?: string
  showRemoteOnly?: boolean
  showLocalOnly?: boolean
  showAliasedOnly?: boolean
  showUnaliasedOnly?: boolean
}

// Bulk alias operation
export interface BulkAliasOperation {
  operations: CreateAliasRequest[]
  strategy: 'skip-existing' | 'overwrite-existing' | 'fail-on-existing'
}

// Alias validation result
export interface AliasValidation {
  isValid: boolean
  errors: string[]
  warnings: string[]
}

// Export/Import format for aliases
export interface AliasExport {
  version: string
  createdAt: string
  aliases: Array<{
    sourceVariableName: string
    sourceCollectionName: string
    targetVariableName: string
    targetCollectionName: string
    modeName: string
  }>
}
