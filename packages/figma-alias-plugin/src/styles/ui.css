/* Reset and base styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: 12px;
  line-height: 1.4;
  color: #333;
  background: #fff;
}

/* Plugin container */
.plugin-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  max-height: 700px;
}

/* Header */
.plugin-header {
  padding: 16px;
  border-bottom: 1px solid #e5e5e5;
  background: #f8f9fa;
}

.plugin-header h1 {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 4px;
  color: #1a1a1a;
}

.plugin-header p {
  font-size: 11px;
  color: #666;
  line-height: 1.3;
  margin-bottom: 12px;
}

/* Tab navigation */
.tab-navigation {
  display: flex;
  gap: 2px;
  background: #e5e5e5;
  border-radius: 4px;
  padding: 2px;
}

.tab-button {
  flex: 1;
  padding: 6px 12px;
  font-size: 11px;
  font-weight: 500;
  background: transparent;
  border: none;
  border-radius: 2px;
  cursor: pointer;
  transition: all 0.2s;
  color: #666;
}

.tab-button.active {
  background: #fff;
  color: #1a1a1a;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.tab-button:hover:not(.active) {
  background: rgba(255, 255, 255, 0.5);
}

/* Content */
.plugin-content {
  flex: 1;
  overflow-y: auto;
  padding: 0;
}

/* Sections */
.section {
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.section:last-child {
  border-bottom: none;
}

.section h2 {
  font-size: 13px;
  font-weight: 600;
  margin-bottom: 4px;
  color: #1a1a1a;
}

.section h3 {
  font-size: 12px;
  font-weight: 600;
  margin-bottom: 8px;
  color: #1a1a1a;
}

.section-description {
  font-size: 11px;
  color: #666;
  margin-bottom: 16px;
  line-height: 1.3;
}

.selection-description {
  font-size: 10px;
  color: #888;
  margin-bottom: 8px;
}

/* Filter Panel */
.filter-panel {
  background: #f8f9fa;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.clear-filters {
  font-size: 10px;
  color: #0066cc;
  background: none;
  border: none;
  cursor: pointer;
  text-decoration: underline;
}

.filter-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  margin-bottom: 12px;
}

.filter-item label {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.filter-item span {
  font-size: 10px;
  font-weight: 500;
  color: #666;
}

.filter-item input,
.filter-item select {
  padding: 4px 6px;
  font-size: 11px;
  border: 1px solid #ddd;
  border-radius: 3px;
  background: #fff;
}

.filter-item input:focus,
.filter-item select:focus {
  outline: none;
  border-color: #0066cc;
}

/* Active filters */
.active-filters {
  padding-top: 12px;
  border-top: 1px solid #e5e5e5;
}

.filter-label {
  font-size: 10px;
  font-weight: 500;
  color: #666;
  margin-bottom: 6px;
  display: block;
}

.filter-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.filter-tag {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 2px 6px;
  font-size: 10px;
  background: #e3f2fd;
  color: #1565c0;
  border-radius: 12px;
}

.filter-tag button {
  background: none;
  border: none;
  color: #1565c0;
  cursor: pointer;
  font-size: 12px;
  line-height: 1;
  padding: 0;
  width: 12px;
  height: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Alias Creator */
.alias-creator {
  display: grid;
  grid-template-columns: 1fr auto 1fr;
  gap: 16px;
  margin-bottom: 16px;
}

.variable-selection {
  min-height: 200px;
}

.collection-selector {
  margin-bottom: 12px;
}

.collection-selector label {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.collection-selector span {
  font-size: 10px;
  font-weight: 500;
  color: #666;
}

.collection-selector select {
  padding: 4px 6px;
  font-size: 11px;
  border: 1px solid #ddd;
  border-radius: 3px;
  background: #fff;
}

.variable-list {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #e5e5e5;
  border-radius: 4px;
}

.variable-item {
  padding: 8px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s;
}

.variable-item:last-child {
  border-bottom: none;
}

.variable-item:hover {
  background: #f8f9fa;
}

.variable-item.selected {
  background: #e3f2fd;
  border-color: #bbdefb;
}

.variable-name {
  font-size: 11px;
  font-weight: 500;
  color: #1a1a1a;
  margin-bottom: 2px;
}

.variable-details {
  font-size: 10px;
  color: #666;
  margin-bottom: 4px;
}

.variable-description {
  font-size: 10px;
  color: #888;
  line-height: 1.3;
}

.remote-badge {
  display: inline-block;
  padding: 1px 4px;
  font-size: 9px;
  background: #fff3cd;
  color: #856404;
  border-radius: 2px;
  margin-left: 4px;
}

.alias-arrow {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 4px;
  padding-top: 40px;
}

.arrow {
  font-size: 18px;
  color: #0066cc;
  font-weight: bold;
}

.alias-arrow span {
  font-size: 10px;
  color: #666;
}

/* Mode Selection */
.mode-selection {
  margin-bottom: 16px;
}

.mode-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.mode-item {
  display: flex;
  align-items: center;
  gap: 4px;
  cursor: pointer;
  font-size: 11px;
}

/* Alias Options */
.alias-options {
  margin-bottom: 16px;
}

.option-item {
  display: flex;
  align-items: center;
  gap: 6px;
  cursor: pointer;
  font-size: 11px;
}

/* Validation */
.validation {
  margin-bottom: 16px;
  padding: 12px;
  border-radius: 4px;
  background: #f8f9fa;
}

.validation-errors {
  margin-bottom: 8px;
}

.validation-errors h4 {
  font-size: 11px;
  color: #c62828;
  margin-bottom: 4px;
}

.validation-errors ul {
  list-style: none;
  padding: 0;
}

.validation-errors li {
  font-size: 10px;
  color: #c62828;
  margin-bottom: 2px;
  padding-left: 12px;
  position: relative;
}

.validation-errors li::before {
  content: '•';
  position: absolute;
  left: 0;
}

.validation-warnings {
  margin-bottom: 8px;
}

.validation-warnings h4 {
  font-size: 11px;
  color: #f57c00;
  margin-bottom: 4px;
}

.validation-warnings ul {
  list-style: none;
  padding: 0;
}

.validation-warnings li {
  font-size: 10px;
  color: #f57c00;
  margin-bottom: 2px;
  padding-left: 12px;
  position: relative;
}

.validation-warnings li::before {
  content: '⚠';
  position: absolute;
  left: 0;
}

/* Create Section */
.create-section {
  text-align: center;
}

.create-button {
  width: 100%;
  padding: 12px;
  font-size: 12px;
  font-weight: 500;
  background: #0066cc;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
  margin-bottom: 8px;
}

.create-button:hover:not(:disabled) {
  background: #0052a3;
}

.create-button:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.alias-preview {
  font-size: 10px;
  color: #666;
  margin: 0;
}

/* Alias Manager */
.alias-filters {
  margin-bottom: 16px;
}

.filter-row {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr;
  gap: 8px;
}

.search-input input,
.filter-select select,
.sort-select select {
  width: 100%;
  padding: 6px 8px;
  font-size: 11px;
  border: 1px solid #ddd;
  border-radius: 3px;
  background: #fff;
}

.alias-list {
  max-height: 400px;
  overflow-y: auto;
}

.alias-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border: 1px solid #e5e5e5;
  border-radius: 4px;
  margin-bottom: 8px;
  background: #fff;
}

.alias-content {
  flex: 1;
}

.alias-main {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 6px;
}

.alias-source,
.alias-target {
  flex: 1;
}

.alias-main .alias-arrow {
  color: #0066cc;
  font-weight: bold;
  padding: 0;
  font-size: 14px;
}

.alias-details {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  align-items: center;
}

.mode-badge,
.library-badge {
  display: inline-block;
  padding: 2px 6px;
  font-size: 9px;
  border-radius: 12px;
  background: #e3f2fd;
  color: #1565c0;
}

.library-badge {
  background: #f3e5f5;
  color: #7b1fa2;
}

.created-date {
  font-size: 9px;
  color: #888;
}

.collection-name {
  font-size: 10px;
  color: #666;
}

.alias-actions {
  margin-left: 12px;
}

.remove-button {
  width: 24px;
  height: 24px;
  border: none;
  background: #ffebee;
  color: #c62828;
  border-radius: 50%;
  cursor: pointer;
  font-size: 14px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.remove-button:hover {
  background: #ffcdd2;
}

.alias-summary {
  padding: 8px 0;
  text-align: center;
  font-size: 10px;
  color: #666;
  border-top: 1px solid #f0f0f0;
}

/* Empty states */
.empty-state {
  text-align: center;
  padding: 32px 16px;
  color: #666;
}

.empty-state h3 {
  font-size: 12px;
  margin-bottom: 4px;
  color: #1a1a1a;
}

.empty-state p {
  font-size: 11px;
  line-height: 1.3;
}

/* Loading */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px 16px;
  text-align: center;
}

.loading-spinner {
  width: 24px;
  height: 24px;
  border: 2px solid #e5e5e5;
  border-top: 2px solid #0066cc;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 12px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading p {
  font-size: 12px;
  color: #666;
}

/* Error */
.error {
  padding: 24px;
  text-align: center;
}

.error h2 {
  font-size: 14px;
  color: #c62828;
  margin-bottom: 8px;
}

.error p {
  font-size: 11px;
  color: #666;
  margin-bottom: 16px;
  line-height: 1.3;
}

.error button {
  padding: 8px 16px;
  font-size: 11px;
  background: #0066cc;
  color: white;
  border: none;
  border-radius: 3px;
  cursor: pointer;
}

/* Footer */
.plugin-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-top: 1px solid #e5e5e5;
  background: #f8f9fa;
}

.stats {
  display: flex;
  gap: 12px;
  font-size: 10px;
  color: #666;
}

.close-button {
  padding: 6px 12px;
  font-size: 11px;
  background: #fff;
  color: #666;
  border: 1px solid #ddd;
  border-radius: 3px;
  cursor: pointer;
  transition: all 0.2s;
}

.close-button:hover {
  background: #f5f5f5;
  border-color: #ccc;
}

/* Create Section */
.create-section {
  text-align: center;
}

.create-button {
  width: 100%;
  padding: 12px;
  font-size: 12px;
  font-weight: 500;
  background: #0066cc;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
  margin-bottom: 8px;
}

.create-button:hover:not(:disabled) {
  background: #0052a3;
}

.create-button:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.alias-preview {
  font-size: 10px;
  color: #666;
  margin: 0;
}

/* Alias Manager */
.alias-filters {
  margin-bottom: 16px;
}

.filter-row {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr;
  gap: 8px;
}

.search-input input,
.filter-select select,
.sort-select select {
  width: 100%;
  padding: 6px 8px;
  font-size: 11px;
  border: 1px solid #ddd;
  border-radius: 3px;
  background: #fff;
}

.alias-list {
  max-height: 400px;
  overflow-y: auto;
}

.alias-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border: 1px solid #e5e5e5;
  border-radius: 4px;
  margin-bottom: 8px;
  background: #fff;
}

.alias-content {
  flex: 1;
}

.alias-main {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 6px;
}

.alias-source,
.alias-target {
  flex: 1;
}

.alias-arrow {
  color: #0066cc;
  font-weight: bold;
  padding: 0;
}

.alias-details {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  align-items: center;
}

.mode-badge,
.library-badge {
  display: inline-block;
  padding: 2px 6px;
  font-size: 9px;
  border-radius: 12px;
  background: #e3f2fd;
  color: #1565c0;
}

.library-badge {
  background: #f3e5f5;
  color: #7b1fa2;
}

.created-date {
  font-size: 9px;
  color: #888;
}

.collection-name {
  font-size: 10px;
  color: #666;
}

.alias-actions {
  margin-left: 12px;
}

.remove-button {
  width: 24px;
  height: 24px;
  border: none;
  background: #ffebee;
  color: #c62828;
  border-radius: 50%;
  cursor: pointer;
  font-size: 14px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.remove-button:hover {
  background: #ffcdd2;
}

.alias-summary {
  padding: 8px 0;
  text-align: center;
  font-size: 10px;
  color: #666;
  border-top: 1px solid #f0f0f0;
}

/* Empty states */
.empty-state {
  text-align: center;
  padding: 32px 16px;
  color: #666;
}

.empty-state h3 {
  font-size: 12px;
  margin-bottom: 4px;
  color: #1a1a1a;
}

.empty-state p {
  font-size: 11px;
  line-height: 1.3;
}

/* Loading */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px 16px;
  text-align: center;
}

.loading-spinner {
  width: 24px;
  height: 24px;
  border: 2px solid #e5e5e5;
  border-top: 2px solid #0066cc;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 12px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading p {
  font-size: 12px;
  color: #666;
}

/* Error */
.error {
  padding: 24px;
  text-align: center;
}

.error h2 {
  font-size: 14px;
  color: #c62828;
  margin-bottom: 8px;
}

.error p {
  font-size: 11px;
  color: #666;
  margin-bottom: 16px;
  line-height: 1.3;
}

.error button {
  padding: 8px 16px;
  font-size: 11px;
  background: #0066cc;
  color: white;
  border: none;
  border-radius: 3px;
  cursor: pointer;
}

/* Footer */
.plugin-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-top: 1px solid #e5e5e5;
  background: #f8f9fa;
}

.stats {
  display: flex;
  gap: 12px;
  font-size: 10px;
  color: #666;
}

.close-button {
  padding: 6px 12px;
  font-size: 11px;
  background: #fff;
  color: #666;
  border: 1px solid #ddd;
  border-radius: 3px;
  cursor: pointer;
  transition: all 0.2s;
}

.close-button:hover {
  background: #f5f5f5;
  border-color: #ccc;
}
