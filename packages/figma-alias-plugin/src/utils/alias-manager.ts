import {
  VariableInfo,
  CollectionInfo,
  VariableAlias,
  CreateAliasRequest,
  AliasOperationResult,
  AliasValidation,
  FilterOptions
} from '../types/alias'

/**
 * Core alias management functionality
 */
export class AliasManager {
  private collections: Map<string, CollectionInfo> = new Map()
  private variables: Map<string, VariableInfo> = new Map()
  private aliases: Map<string, VariableAlias> = new Map()

  /**
   * Load all collections and variables
   */
  async loadData(): Promise<void> {
    try {
      // Load local collections
      const localCollections = await figma.variables.getLocalVariableCollectionsAsync()
      
      for (const collection of localCollections) {
        const collectionInfo: CollectionInfo = {
          id: collection.id,
          name: collection.name,
          description: collection.description,
          remote: collection.remote,
          hiddenFromPublishing: collection.hiddenFromPublishing,
          modes: collection.modes.map(mode => ({
            modeId: mode.modeId,
            name: mode.name
          })),
          variableIds: collection.variableIds,
          variableCount: collection.variableIds.length
        }
        
        this.collections.set(collection.id, collectionInfo)

        // Load variables for this collection
        for (const variableId of collection.variableIds) {
          const variable = await figma.variables.getVariableByIdAsync(variableId)
          if (variable) {
            const variableInfo: VariableInfo = {
              id: variable.id,
              name: variable.name,
              key: variable.key,
              resolvedType: variable.resolvedType,
              scopes: variable.scopes,
              description: variable.description,
              collectionId: variable.variableCollectionId,
              collectionName: collection.name,
              remote: variable.remote,
              hiddenFromPublishing: variable.hiddenFromPublishing,
              modes: collection.modes.map(mode => ({
                modeId: mode.modeId,
                name: mode.name,
                value: variable.valuesByMode[mode.modeId]
              }))
            }
            
            this.variables.set(variable.id, variableInfo)
          }
        }
      }

      // Scan for existing aliases
      await this.scanExistingAliases()

    } catch (error) {
      console.error('Error loading data:', error)
      throw error
    }
  }

  /**
   * Scan for existing variable aliases
   */
  private async scanExistingAliases(): Promise<void> {
    this.aliases.clear()

    for (const [variableId, variable] of this.variables) {
      for (const mode of variable.modes) {
        const value = mode.value
        
        // Check if the value is a variable alias
        if (value && typeof value === 'object' && 'type' in value && value.type === 'VARIABLE_ALIAS') {
          const aliasValue = value as VariableAlias
          const targetVariable = this.variables.get(aliasValue.id)
          
          if (targetVariable) {
            const alias: VariableAlias = {
              id: `${variableId}-${mode.modeId}`,
              sourceVariableId: variableId,
              sourceVariableName: variable.name,
              sourceCollectionName: variable.collectionName,
              targetVariableId: aliasValue.id,
              targetVariableName: targetVariable.name,
              targetCollectionName: targetVariable.collectionName,
              modeId: mode.modeId,
              modeName: mode.name,
              createdAt: new Date().toISOString()
            }
            
            this.aliases.set(alias.id, alias)
          }
        }
      }
    }
  }

  /**
   * Create a new variable alias
   */
  async createAlias(request: CreateAliasRequest): Promise<AliasOperationResult> {
    try {
      const validation = this.validateAlias(request)
      if (!validation.isValid) {
        return {
          success: false,
          message: 'Validation failed',
          error: validation.errors.join(', ')
        }
      }

      const sourceVariable = await figma.variables.getVariableByIdAsync(request.sourceVariableId)
      const targetVariable = await figma.variables.getVariableByIdAsync(request.targetVariableId)

      if (!sourceVariable || !targetVariable) {
        return {
          success: false,
          message: 'Source or target variable not found',
          error: 'Variables not found'
        }
      }

      // Check if alias already exists
      const existingAliasId = `${request.sourceVariableId}-${request.modeId}`
      const existingAlias = this.aliases.get(existingAliasId)
      
      if (existingAlias && !request.overwriteExisting) {
        return {
          success: false,
          message: 'Alias already exists for this variable and mode',
          error: 'Alias exists'
        }
      }

      // Create the alias
      sourceVariable.setValueForMode(request.modeId, {
        type: 'VARIABLE_ALIAS',
        id: request.targetVariableId
      })

      // Update our internal tracking
      const sourceInfo = this.variables.get(request.sourceVariableId)!
      const targetInfo = this.variables.get(request.targetVariableId)!
      const mode = sourceInfo.modes.find(m => m.modeId === request.modeId)!

      const alias: VariableAlias = {
        id: existingAliasId,
        sourceVariableId: request.sourceVariableId,
        sourceVariableName: sourceInfo.name,
        sourceCollectionName: sourceInfo.collectionName,
        targetVariableId: request.targetVariableId,
        targetVariableName: targetInfo.name,
        targetCollectionName: targetInfo.collectionName,
        modeId: request.modeId,
        modeName: mode.name,
        createdAt: new Date().toISOString()
      }

      this.aliases.set(alias.id, alias)

      return {
        success: true,
        message: `Alias created: ${sourceInfo.name} → ${targetInfo.name}`,
        aliasId: alias.id
      }

    } catch (error) {
      console.error('Error creating alias:', error)
      return {
        success: false,
        message: 'Failed to create alias',
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Remove a variable alias
   */
  async removeAlias(aliasId: string): Promise<AliasOperationResult> {
    try {
      const alias = this.aliases.get(aliasId)
      if (!alias) {
        return {
          success: false,
          message: 'Alias not found',
          error: 'Alias not found'
        }
      }

      const sourceVariable = await figma.variables.getVariableByIdAsync(alias.sourceVariableId)
      if (!sourceVariable) {
        return {
          success: false,
          message: 'Source variable not found',
          error: 'Variable not found'
        }
      }

      // Remove the alias by setting the value to null or a default value
      // Note: In Figma, you typically need to set a concrete value, not null
      const targetVariable = await figma.variables.getVariableByIdAsync(alias.targetVariableId)
      if (targetVariable) {
        const targetValue = targetVariable.valuesByMode[alias.modeId]
        sourceVariable.setValueForMode(alias.modeId, targetValue)
      }

      // Remove from our tracking
      this.aliases.delete(aliasId)

      return {
        success: true,
        message: `Alias removed: ${alias.sourceVariableName}`,
        aliasId
      }

    } catch (error) {
      console.error('Error removing alias:', error)
      return {
        success: false,
        message: 'Failed to remove alias',
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Validate an alias creation request
   */
  validateAlias(request: CreateAliasRequest): AliasValidation {
    const errors: string[] = []
    const warnings: string[] = []

    const sourceVariable = this.variables.get(request.sourceVariableId)
    const targetVariable = this.variables.get(request.targetVariableId)

    if (!sourceVariable) {
      errors.push('Source variable not found')
    }

    if (!targetVariable) {
      errors.push('Target variable not found')
    }

    if (sourceVariable && targetVariable) {
      // Check if variables are compatible
      if (sourceVariable.resolvedType !== targetVariable.resolvedType) {
        errors.push(`Variable types don't match: ${sourceVariable.resolvedType} vs ${targetVariable.resolvedType}`)
      }

      // Check if creating a circular reference
      if (request.sourceVariableId === request.targetVariableId) {
        errors.push('Cannot create alias to self')
      }

      // Check if mode exists
      const modeExists = sourceVariable.modes.some(m => m.modeId === request.modeId)
      if (!modeExists) {
        errors.push('Mode not found in source variable')
      }

      // Check for potential circular references
      if (this.wouldCreateCircularReference(request.sourceVariableId, request.targetVariableId)) {
        errors.push('Would create circular reference')
      }

      // Warnings
      if (sourceVariable.collectionId === targetVariable.collectionId) {
        warnings.push('Creating alias within the same collection')
      }

      if (targetVariable.remote) {
        warnings.push('Target variable is from a remote library')
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    }
  }

  /**
   * Check if creating an alias would create a circular reference
   */
  private wouldCreateCircularReference(sourceId: string, targetId: string): boolean {
    const visited = new Set<string>()
    
    const checkCircular = (currentId: string): boolean => {
      if (visited.has(currentId)) {
        return true
      }
      
      visited.add(currentId)
      
      // Find all aliases where this variable is the source
      for (const alias of this.aliases.values()) {
        if (alias.sourceVariableId === currentId) {
          if (alias.targetVariableId === sourceId) {
            return true
          }
          if (checkCircular(alias.targetVariableId)) {
            return true
          }
        }
      }
      
      return false
    }
    
    return checkCircular(targetId)
  }

  /**
   * Get filtered variables
   */
  getFilteredVariables(filters: FilterOptions): VariableInfo[] {
    let variables = Array.from(this.variables.values())

    if (filters.variableType) {
      variables = variables.filter(v => v.resolvedType === filters.variableType)
    }

    if (filters.collectionId) {
      variables = variables.filter(v => v.collectionId === filters.collectionId)
    }

    if (filters.searchTerm) {
      const term = filters.searchTerm.toLowerCase()
      variables = variables.filter(v => 
        v.name.toLowerCase().includes(term) ||
        v.description.toLowerCase().includes(term)
      )
    }

    if (filters.showRemoteOnly) {
      variables = variables.filter(v => v.remote)
    }

    if (filters.showLocalOnly) {
      variables = variables.filter(v => !v.remote)
    }

    if (filters.showAliasedOnly) {
      const aliasedVariableIds = new Set(
        Array.from(this.aliases.values()).map(a => a.sourceVariableId)
      )
      variables = variables.filter(v => aliasedVariableIds.has(v.id))
    }

    if (filters.showUnaliasedOnly) {
      const aliasedVariableIds = new Set(
        Array.from(this.aliases.values()).map(a => a.sourceVariableId)
      )
      variables = variables.filter(v => !aliasedVariableIds.has(v.id))
    }

    return variables
  }

  /**
   * Get all collections
   */
  getCollections(): CollectionInfo[] {
    return Array.from(this.collections.values())
  }

  /**
   * Get all variables
   */
  getVariables(): VariableInfo[] {
    return Array.from(this.variables.values())
  }

  /**
   * Get all aliases
   */
  getAliases(): VariableAlias[] {
    return Array.from(this.aliases.values())
  }

  /**
   * Get variable by ID
   */
  getVariable(id: string): VariableInfo | undefined {
    return this.variables.get(id)
  }

  /**
   * Get collection by ID
   */
  getCollection(id: string): CollectionInfo | undefined {
    return this.collections.get(id)
  }
}
