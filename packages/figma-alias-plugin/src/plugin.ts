import { AliasManager } from './utils/alias-manager'
import { CreateAliasRequest } from './types/alias'

// Initialize the alias manager
const aliasManager = new AliasManager()

// Show the plugin UI
figma.showUI(__html__, {
  width: 500,
  height: 700,
  title: 'Variable Alias Manager'
})

// Handle messages from the UI
figma.ui.onmessage = async (msg) => {
  console.log('hi figma');
  switch (msg.type) {
    case 'load-data':
      await handleLoadData()
      break

    case 'create-alias':
      await handleCreateAlias(msg.request)
      break

    case 'remove-alias':
      await handleRemoveAlias(msg.aliasId)
      break

    case 'validate-alias':
      await handleValidateAlias(msg.request)
      break

    case 'get-filtered-variables':
      await handleGetFilteredVariables(msg.filters)
      break

    case 'close':
      figma.closePlugin()
      break

    default:
      console.warn('Unknown message type:', msg.type)
  }
}

/**
 * Load all data and send to UI
 */
async function handleLoadData() {
  try {
    figma.ui.postMessage({
      type: 'loading',
      message: 'Loading collections and variables...'
    })

    await aliasManager.loadData()

    const collections = aliasManager.getCollections()
    const variables = aliasManager.getVariables()
    const aliases = aliasManager.getAliases()

    figma.ui.postMessage({
      type: 'data-loaded',
      data: {
        collections,
        variables,
        aliases
      }
    })

    figma.notify(`Loaded ${collections.length} collections with ${variables.length} variables`)

  } catch (error) {
    console.error('Error loading data:', error)
    figma.ui.postMessage({
      type: 'error',
      message: error instanceof Error ? error.message : 'Failed to load data'
    })
    figma.notify('Failed to load data', { error: true })
  }
}

/**
 * Create a new alias
 */
async function handleCreateAlias(request: CreateAliasRequest) {
  try {
    figma.ui.postMessage({
      type: 'loading',
      message: 'Creating alias...'
    })

    const result = await aliasManager.createAlias(request)

    figma.ui.postMessage({
      type: 'alias-operation-result',
      result
    })

    if (result.success) {
      figma.notify(result.message)

      // Send updated aliases
      const aliases = aliasManager.getAliases()
      figma.ui.postMessage({
        type: 'aliases-updated',
        aliases
      })
    } else {
      figma.notify(result.message, { error: true })
    }

  } catch (error) {
    console.error('Error creating alias:', error)
    figma.ui.postMessage({
      type: 'error',
      message: error instanceof Error ? error.message : 'Failed to create alias'
    })
    figma.notify('Failed to create alias', { error: true })
  }
}

/**
 * Remove an alias
 */
async function handleRemoveAlias(aliasId: string) {
  try {
    figma.ui.postMessage({
      type: 'loading',
      message: 'Removing alias...'
    })

    const result = await aliasManager.removeAlias(aliasId)

    figma.ui.postMessage({
      type: 'alias-operation-result',
      result
    })

    if (result.success) {
      figma.notify(result.message)

      // Send updated aliases
      const aliases = aliasManager.getAliases()
      figma.ui.postMessage({
        type: 'aliases-updated',
        aliases
      })
    } else {
      figma.notify(result.message, { error: true })
    }

  } catch (error) {
    console.error('Error removing alias:', error)
    figma.ui.postMessage({
      type: 'error',
      message: error instanceof Error ? error.message : 'Failed to remove alias'
    })
    figma.notify('Failed to remove alias', { error: true })
  }
}

/**
 * Validate an alias
 */
async function handleValidateAlias(request: CreateAliasRequest) {
  try {
    const validation = aliasManager.validateAlias(request)

    figma.ui.postMessage({
      type: 'alias-validation',
      validation
    })

  } catch (error) {
    console.error('Error validating alias:', error)
    figma.ui.postMessage({
      type: 'error',
      message: error instanceof Error ? error.message : 'Failed to validate alias'
    })
  }
}

/**
 * Get filtered variables
 */
async function handleGetFilteredVariables(filters: any) {
  try {
    const variables = aliasManager.getFilteredVariables(filters)

    figma.ui.postMessage({
      type: 'filtered-variables',
      variables
    })

  } catch (error) {
    console.error('Error filtering variables:', error)
    figma.ui.postMessage({
      type: 'error',
      message: error instanceof Error ? error.message : 'Failed to filter variables'
    })
  }
}

// Initialize the plugin
async function init() {
  // Auto-load data on startup
  await handleLoadData()
}

// Start the plugin
init()
