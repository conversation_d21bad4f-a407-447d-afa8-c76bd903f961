import React, { useState, useEffect } from 'react'
import { VariableInfo, CollectionInfo, VariableAlias, CreateAliasRequest, AliasOperationResult, AliasValidation, FilterOptions } from '../types/alias'
// import { VariableSelector } from './VariableSelector'
import { AliasCreator } from './AliasCreator'
import { AliasManager } from './AliasManager'
import { FilterPanel } from './FilterPanel'

interface AppState {
  collections: CollectionInfo[]
  variables: VariableInfo[]
  aliases: VariableAlias[]
  filteredVariables: VariableInfo[]
  selectedSourceVariable?: VariableInfo
  selectedTargetVariable?: VariableInfo
  selectedMode?: string
  filters: FilterOptions
  isLoading: boolean
  error?: string
  currentTab: 'create' | 'manage'
  validation?: AliasValidation
}

export const App: React.FC = () => {
  const [state, setState] = useState<AppState>({
    collections: [],
    variables: [],
    aliases: [],
    filteredVariables: [],
    filters: {},
    isLoading: true,
    currentTab: 'create'
  })

  useEffect(() => {
    // Listen for messages from the plugin
    window.onmessage = (event) => {
      const { type, ...data } = event.data.pluginMessage

      switch (type) {
        case 'loading':
          setState(prev => ({
            ...prev,
            isLoading: true,
            error: undefined
          }))
          break

        case 'data-loaded':
          setState(prev => ({
            ...prev,
            isLoading: false,
            collections: data.data.collections,
            variables: data.data.variables,
            aliases: data.data.aliases,
            filteredVariables: data.data.variables
          }))
          break

        case 'aliases-updated':
          setState(prev => ({
            ...prev,
            aliases: data.aliases
          }))
          break

        case 'filtered-variables':
          setState(prev => ({
            ...prev,
            filteredVariables: data.variables
          }))
          break

        case 'alias-validation':
          setState(prev => ({
            ...prev,
            validation: data.validation
          }))
          break

        case 'alias-operation-result':
          // Handle operation result if needed
          break

        case 'error':
          setState(prev => ({
            ...prev,
            isLoading: false,
            error: data.message
          }))
          break
      }
    }

    // Request initial data load
    parent.postMessage({
      pluginMessage: { type: 'load-data' }
    }, '*')
  }, [])

  const handleSourceVariableSelect = (variable: VariableInfo) => {
    setState(prev => ({
      ...prev,
      selectedSourceVariable: variable,
      validation: undefined
    }))
  }

  const handleTargetVariableSelect = (variable: VariableInfo) => {
    setState(prev => ({
      ...prev,
      selectedTargetVariable: variable,
      validation: undefined
    }))
  }

  const handleModeSelect = (modeId: string) => {
    setState(prev => ({
      ...prev,
      selectedMode: modeId,
      validation: undefined
    }))
  }

  const handleCreateAlias = (request: CreateAliasRequest) => {
    parent.postMessage({
      pluginMessage: {
        type: 'create-alias',
        request
      }
    }, '*')
  }

  const handleRemoveAlias = (aliasId: string) => {
    parent.postMessage({
      pluginMessage: {
        type: 'remove-alias',
        aliasId
      }
    }, '*')
  }

  const handleValidateAlias = (request: CreateAliasRequest) => {
    parent.postMessage({
      pluginMessage: {
        type: 'validate-alias',
        request
      }
    }, '*')
  }

  const handleFiltersChange = (newFilters: FilterOptions) => {
    setState(prev => ({
      ...prev,
      filters: newFilters
    }))

    parent.postMessage({
      pluginMessage: {
        type: 'get-filtered-variables',
        filters: newFilters
      }
    }, '*')
  }

  const handleClose = () => {
    parent.postMessage({
      pluginMessage: { type: 'close' }
    }, '*')
  }

  const handleTabChange = (tab: 'create' | 'manage') => {
    setState(prev => ({
      ...prev,
      currentTab: tab
    }))
  }

  if (state.isLoading) {
    return (
      <div className="plugin-container">
        <div className="loading">
          <div className="loading-spinner"></div>
          <p>Loading variables and collections...</p>
        </div>
      </div>
    )
  }

  if (state.error) {
    return (
      <div className="plugin-container">
        <div className="error">
          <h2>Error</h2>
          <p>{state.error}</p>
          <button onClick={() => window.location.reload()}>
            Retry
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="plugin-container">
      <div className="plugin-header">
        <h1>Variable Alias Manager</h1>
        <p>Create and manage variable aliases between collections and libraries</p>

        <div className="tab-navigation">
          <button
            className={`tab-button ${state.currentTab === 'create' ? 'active' : ''}`}
            onClick={() => handleTabChange('create')}
          >
            Create Aliases
          </button>
          <button
            className={`tab-button ${state.currentTab === 'manage' ? 'active' : ''}`}
            onClick={() => handleTabChange('manage')}
          >
            Manage Aliases ({state.aliases.length})
          </button>
        </div>
      </div>

      <div className="plugin-content">
        {state.currentTab === 'create' ? (
          <>
            <FilterPanel
              collections={state.collections}
              filters={state.filters}
              onFiltersChange={handleFiltersChange}
            />

            <AliasCreator
              collections={state.collections}
              variables={state.filteredVariables}
              selectedSourceVariable={state.selectedSourceVariable}
              selectedTargetVariable={state.selectedTargetVariable}
              selectedMode={state.selectedMode}
              validation={state.validation}
              onSourceVariableSelect={handleSourceVariableSelect}
              onTargetVariableSelect={handleTargetVariableSelect}
              onModeSelect={handleModeSelect}
              onCreateAlias={handleCreateAlias}
              onValidateAlias={handleValidateAlias}
            />
          </>
        ) : (
          <AliasManager
            aliases={state.aliases}
            variables={state.variables}
            collections={state.collections}
            onRemoveAlias={handleRemoveAlias}
          />
        )}
      </div>

      <div className="plugin-footer">
        <div className="stats">
          <span>{state.collections.length} collections</span>
          <span>{state.variables.length} variables</span>
          <span>{state.aliases.length} aliases</span>
        </div>
        <button className="close-button" onClick={handleClose}>
          Close
        </button>
      </div>
    </div>
  )
}
