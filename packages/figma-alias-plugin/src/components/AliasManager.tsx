import React, { useState } from 'react'
import { VariableAlias, VariableInfo, CollectionInfo } from '../types/alias'

interface AliasManagerProps {
  aliases: VariableAlias[]
  variables: VariableInfo[]
  collections: CollectionInfo[]
  onRemoveAlias: (aliasId: string) => void
}

export const AliasManager: React.FC<AliasManagerProps> = ({
  aliases,
  variables,
  collections,
  onRemoveAlias
}) => {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCollection, setSelectedCollection] = useState('')
  const [sortBy, setSortBy] = useState<'name' | 'collection' | 'created'>('name')

  // Filter and sort aliases
  const filteredAliases = aliases
    .filter(alias => {
      const matchesSearch = !searchTerm || 
        alias.sourceVariableName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        alias.targetVariableName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        alias.sourceCollectionName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        alias.targetCollectionName.toLowerCase().includes(searchTerm.toLowerCase())
      
      const matchesCollection = !selectedCollection ||
        alias.sourceCollectionName === selectedCollection ||
        alias.targetCollectionName === selectedCollection
      
      return matchesSearch && matchesCollection
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.sourceVariableName.localeCompare(b.sourceVariableName)
        case 'collection':
          return a.sourceCollectionName.localeCompare(b.sourceCollectionName)
        case 'created':
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
        default:
          return 0
      }
    })

  const handleRemoveAlias = (aliasId: string, aliasName: string) => {
    if (confirm(`Are you sure you want to remove the alias "${aliasName}"?`)) {
      onRemoveAlias(aliasId)
    }
  }

  // Get unique collection names for filter
  const collectionNames = Array.from(new Set([
    ...aliases.map(a => a.sourceCollectionName),
    ...aliases.map(a => a.targetCollectionName)
  ])).sort()

  return (
    <div className="section">
      <h2>Manage Aliases</h2>
      <p className="section-description">
        View and manage existing variable aliases. Remove aliases to break the connection between variables.
      </p>

      {/* Filters and Search */}
      <div className="alias-filters">
        <div className="filter-row">
          <div className="search-input">
            <input
              type="text"
              placeholder="Search aliases..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          
          <div className="filter-select">
            <select
              value={selectedCollection}
              onChange={(e) => setSelectedCollection(e.target.value)}
            >
              <option value="">All Collections</option>
              {collectionNames.map(name => (
                <option key={name} value={name}>{name}</option>
              ))}
            </select>
          </div>
          
          <div className="sort-select">
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as 'name' | 'collection' | 'created')}
            >
              <option value="name">Sort by Name</option>
              <option value="collection">Sort by Collection</option>
              <option value="created">Sort by Created</option>
            </select>
          </div>
        </div>
      </div>

      {/* Alias List */}
      <div className="alias-list">
        {filteredAliases.length === 0 ? (
          <div className="empty-state">
            {aliases.length === 0 ? (
              <div>
                <h3>No aliases found</h3>
                <p>Create your first alias using the "Create Aliases" tab.</p>
              </div>
            ) : (
              <div>
                <h3>No aliases match your filters</h3>
                <p>Try adjusting your search or filter criteria.</p>
              </div>
            )}
          </div>
        ) : (
          filteredAliases.map(alias => (
            <div key={alias.id} className="alias-item">
              <div className="alias-content">
                <div className="alias-main">
                  <div className="alias-source">
                    <div className="variable-name">{alias.sourceVariableName}</div>
                    <div className="collection-name">{alias.sourceCollectionName}</div>
                  </div>
                  
                  <div className="alias-arrow">→</div>
                  
                  <div className="alias-target">
                    <div className="variable-name">{alias.targetVariableName}</div>
                    <div className="collection-name">{alias.targetCollectionName}</div>
                  </div>
                </div>
                
                <div className="alias-details">
                  <span className="mode-badge">{alias.modeName}</span>
                  {alias.sourceLibraryName && (
                    <span className="library-badge">From: {alias.sourceLibraryName}</span>
                  )}
                  {alias.targetLibraryName && (
                    <span className="library-badge">To: {alias.targetLibraryName}</span>
                  )}
                  <span className="created-date">
                    Created: {new Date(alias.createdAt).toLocaleDateString()}
                  </span>
                </div>
              </div>
              
              <div className="alias-actions">
                <button
                  className="remove-button"
                  onClick={() => handleRemoveAlias(alias.id, alias.sourceVariableName)}
                  title="Remove alias"
                >
                  ×
                </button>
              </div>
            </div>
          ))
        )}
      </div>

      {/* Summary */}
      {aliases.length > 0 && (
        <div className="alias-summary">
          <p>
            Showing {filteredAliases.length} of {aliases.length} aliases
          </p>
        </div>
      )}
    </div>
  )
}
