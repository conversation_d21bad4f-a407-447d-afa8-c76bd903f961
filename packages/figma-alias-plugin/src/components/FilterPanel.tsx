import React from 'react'
import { CollectionInfo, FilterOptions } from '../types/alias'

interface FilterPanelProps {
  collections: CollectionInfo[]
  filters: FilterOptions
  onFiltersChange: (filters: FilterOptions) => void
}

export const FilterPanel: React.FC<FilterPanelProps> = ({
  collections,
  filters,
  onFiltersChange
}) => {
  const handleFilterChange = (key: keyof FilterOptions, value: any) => {
    onFiltersChange({
      ...filters,
      [key]: value
    })
  }

  const clearFilters = () => {
    onFiltersChange({})
  }

  const hasActiveFilters = Object.keys(filters).some(key => {
    const value = filters[key as keyof FilterOptions]
    return value !== undefined && value !== '' && value !== false
  })

  return (
    <div className="section filter-panel">
      <div className="filter-header">
        <h3>Filter Variables</h3>
        {hasActiveFilters && (
          <button className="clear-filters" onClick={clearFilters}>
            Clear Filters
          </button>
        )}
      </div>

      <div className="filter-grid">
        {/* Search */}
        <div className="filter-item">
          <label>
            <span>Search:</span>
            <input
              type="text"
              placeholder="Variable name or description..."
              value={filters.searchTerm || ''}
              onChange={(e) => handleFilterChange('searchTerm', e.target.value)}
            />
          </label>
        </div>

        {/* Variable Type */}
        <div className="filter-item">
          <label>
            <span>Type:</span>
            <select
              value={filters.variableType || ''}
              onChange={(e) => handleFilterChange('variableType', e.target.value || undefined)}
            >
              <option value="">All Types</option>
              <option value="COLOR">Color</option>
              <option value="FLOAT">Number</option>
              <option value="STRING">String</option>
              <option value="BOOLEAN">Boolean</option>
            </select>
          </label>
        </div>

        {/* Collection */}
        <div className="filter-item">
          <label>
            <span>Collection:</span>
            <select
              value={filters.collectionId || ''}
              onChange={(e) => handleFilterChange('collectionId', e.target.value || undefined)}
            >
              <option value="">All Collections</option>
              {collections.map(collection => (
                <option key={collection.id} value={collection.id}>
                  {collection.name} ({collection.variableCount})
                </option>
              ))}
            </select>
          </label>
        </div>

        {/* Remote/Local */}
        <div className="filter-item">
          <label>
            <span>Source:</span>
            <select
              value={
                filters.showRemoteOnly ? 'remote' :
                filters.showLocalOnly ? 'local' : ''
              }
              onChange={(e) => {
                const value = e.target.value
                handleFilterChange('showRemoteOnly', value === 'remote')
                handleFilterChange('showLocalOnly', value === 'local')
              }}
            >
              <option value="">All Sources</option>
              <option value="local">Local Only</option>
              <option value="remote">Remote Only</option>
            </select>
          </label>
        </div>

        {/* Alias Status */}
        <div className="filter-item">
          <label>
            <span>Alias Status:</span>
            <select
              value={
                filters.showAliasedOnly ? 'aliased' :
                filters.showUnaliasedOnly ? 'unaliased' : ''
              }
              onChange={(e) => {
                const value = e.target.value
                handleFilterChange('showAliasedOnly', value === 'aliased')
                handleFilterChange('showUnaliasedOnly', value === 'unaliased')
              }}
            >
              <option value="">All Variables</option>
              <option value="unaliased">Available for Aliasing</option>
              <option value="aliased">Already Aliased</option>
            </select>
          </label>
        </div>
      </div>

      {hasActiveFilters && (
        <div className="active-filters">
          <span className="filter-label">Active filters:</span>
          <div className="filter-tags">
            {filters.searchTerm && (
              <span className="filter-tag">
                Search: "{filters.searchTerm}"
                <button onClick={() => handleFilterChange('searchTerm', undefined)}>×</button>
              </span>
            )}
            {filters.variableType && (
              <span className="filter-tag">
                Type: {filters.variableType}
                <button onClick={() => handleFilterChange('variableType', undefined)}>×</button>
              </span>
            )}
            {filters.collectionId && (
              <span className="filter-tag">
                Collection: {collections.find(c => c.id === filters.collectionId)?.name}
                <button onClick={() => handleFilterChange('collectionId', undefined)}>×</button>
              </span>
            )}
            {filters.showRemoteOnly && (
              <span className="filter-tag">
                Remote Only
                <button onClick={() => handleFilterChange('showRemoteOnly', false)}>×</button>
              </span>
            )}
            {filters.showLocalOnly && (
              <span className="filter-tag">
                Local Only
                <button onClick={() => handleFilterChange('showLocalOnly', false)}>×</button>
              </span>
            )}
            {filters.showAliasedOnly && (
              <span className="filter-tag">
                Aliased Only
                <button onClick={() => handleFilterChange('showAliasedOnly', false)}>×</button>
              </span>
            )}
            {filters.showUnaliasedOnly && (
              <span className="filter-tag">
                Unaliased Only
                <button onClick={() => handleFilterChange('showUnaliasedOnly', false)}>×</button>
              </span>
            )}
          </div>
        </div>
      )}
    </div>
  )
}
