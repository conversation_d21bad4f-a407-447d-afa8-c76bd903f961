import React, { useState, useEffect } from 'react'
import { VariableInfo, CollectionInfo, CreateAliasRequest, AliasValidation } from '../types/alias'

interface AliasCreatorProps {
  collections: CollectionInfo[]
  variables: VariableInfo[]
  selectedSourceVariable?: VariableInfo
  selectedTargetVariable?: VariableInfo
  selectedMode?: string
  validation?: AliasValidation
  onSourceVariableSelect: (variable: VariableInfo) => void
  onTargetVariableSelect: (variable: VariableInfo) => void
  onModeSelect: (modeId: string) => void
  onCreateAlias: (request: CreateAliasRequest) => void
  onValidateAlias: (request: CreateAliasRequest) => void
}

export const AliasCreator: React.FC<AliasCreatorProps> = ({
  collections,
  variables,
  selectedSourceVariable,
  selectedTargetVariable,
  selectedMode,
  validation,
  onSourceVariableSelect,
  onTargetVariableSelect,
  onModeSelect,
  onCreateAlias,
  onValidateAlias
}) => {
  const [sourceCollectionId, setSourceCollectionId] = useState<string>('')
  const [targetCollectionId, setTargetCollectionId] = useState<string>('')
  const [overwriteExisting, setOverwriteExisting] = useState(false)

  // Filter variables by collection
  const sourceVariables = variables.filter(v => 
    !sourceCollectionId || v.collectionId === sourceCollectionId
  )
  const targetVariables = variables.filter(v => 
    !targetCollectionId || v.collectionId === targetCollectionId
  )

  // Get available modes from source variable
  const availableModes = selectedSourceVariable?.modes || []

  // Auto-validate when selection changes
  useEffect(() => {
    if (selectedSourceVariable && selectedTargetVariable && selectedMode) {
      const request: CreateAliasRequest = {
        sourceVariableId: selectedSourceVariable.id,
        targetVariableId: selectedTargetVariable.id,
        modeId: selectedMode,
        overwriteExisting
      }
      onValidateAlias(request)
    }
  }, [selectedSourceVariable, selectedTargetVariable, selectedMode, overwriteExisting, onValidateAlias])

  const handleCreateAlias = () => {
    if (selectedSourceVariable && selectedTargetVariable && selectedMode) {
      const request: CreateAliasRequest = {
        sourceVariableId: selectedSourceVariable.id,
        targetVariableId: selectedTargetVariable.id,
        modeId: selectedMode,
        overwriteExisting
      }
      onCreateAlias(request)
    }
  }

  const canCreateAlias = selectedSourceVariable && selectedTargetVariable && selectedMode && 
    validation?.isValid

  return (
    <div className="section">
      <h2>Create Variable Alias</h2>
      <p className="section-description">
        Create an alias from a source variable to a target variable. The source variable will reference the target variable's value.
      </p>

      <div className="alias-creator">
        {/* Source Variable Selection */}
        <div className="variable-selection">
          <h3>Source Variable</h3>
          <p className="selection-description">The variable that will reference another variable</p>
          
          <div className="collection-selector">
            <label>
              <span>Source Collection:</span>
              <select
                value={sourceCollectionId}
                onChange={(e) => setSourceCollectionId(e.target.value)}
              >
                <option value="">All Collections</option>
                {collections.map(collection => (
                  <option key={collection.id} value={collection.id}>
                    {collection.name} ({collection.variableCount} variables)
                  </option>
                ))}
              </select>
            </label>
          </div>

          <div className="variable-list">
            {sourceVariables.length === 0 ? (
              <p className="empty-state">No variables found</p>
            ) : (
              sourceVariables.map(variable => (
                <div
                  key={variable.id}
                  className={`variable-item ${selectedSourceVariable?.id === variable.id ? 'selected' : ''}`}
                  onClick={() => onSourceVariableSelect(variable)}
                >
                  <div className="variable-info">
                    <div className="variable-name">{variable.name}</div>
                    <div className="variable-details">
                      {variable.resolvedType} • {variable.collectionName}
                      {variable.remote && <span className="remote-badge">Remote</span>}
                    </div>
                    {variable.description && (
                      <div className="variable-description">{variable.description}</div>
                    )}
                  </div>
                </div>
              ))
            )}
          </div>
        </div>

        {/* Arrow */}
        <div className="alias-arrow">
          <div className="arrow">→</div>
          <span>aliases to</span>
        </div>

        {/* Target Variable Selection */}
        <div className="variable-selection">
          <h3>Target Variable</h3>
          <p className="selection-description">The variable to reference</p>
          
          <div className="collection-selector">
            <label>
              <span>Target Collection:</span>
              <select
                value={targetCollectionId}
                onChange={(e) => setTargetCollectionId(e.target.value)}
              >
                <option value="">All Collections</option>
                {collections.map(collection => (
                  <option key={collection.id} value={collection.id}>
                    {collection.name} ({collection.variableCount} variables)
                  </option>
                ))}
              </select>
            </label>
          </div>

          <div className="variable-list">
            {targetVariables.length === 0 ? (
              <p className="empty-state">No variables found</p>
            ) : (
              targetVariables.map(variable => (
                <div
                  key={variable.id}
                  className={`variable-item ${selectedTargetVariable?.id === variable.id ? 'selected' : ''}`}
                  onClick={() => onTargetVariableSelect(variable)}
                >
                  <div className="variable-info">
                    <div className="variable-name">{variable.name}</div>
                    <div className="variable-details">
                      {variable.resolvedType} • {variable.collectionName}
                      {variable.remote && <span className="remote-badge">Remote</span>}
                    </div>
                    {variable.description && (
                      <div className="variable-description">{variable.description}</div>
                    )}
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
      </div>

      {/* Mode Selection */}
      {selectedSourceVariable && (
        <div className="mode-selection">
          <h3>Mode</h3>
          <p className="selection-description">Select which mode to create the alias for</p>
          
          <div className="mode-list">
            {availableModes.map(mode => (
              <label key={mode.modeId} className="mode-item">
                <input
                  type="radio"
                  name="mode"
                  value={mode.modeId}
                  checked={selectedMode === mode.modeId}
                  onChange={(e) => onModeSelect(e.target.value)}
                />
                <span>{mode.name}</span>
              </label>
            ))}
          </div>
        </div>
      )}

      {/* Options */}
      <div className="alias-options">
        <label className="option-item">
          <input
            type="checkbox"
            checked={overwriteExisting}
            onChange={(e) => setOverwriteExisting(e.target.checked)}
          />
          <span>Overwrite existing alias</span>
        </label>
      </div>

      {/* Validation */}
      {validation && (
        <div className="validation">
          {validation.errors.length > 0 && (
            <div className="validation-errors">
              <h4>Errors:</h4>
              <ul>
                {validation.errors.map((error, index) => (
                  <li key={index}>{error}</li>
                ))}
              </ul>
            </div>
          )}
          
          {validation.warnings.length > 0 && (
            <div className="validation-warnings">
              <h4>Warnings:</h4>
              <ul>
                {validation.warnings.map((warning, index) => (
                  <li key={index}>{warning}</li>
                ))}
              </ul>
            </div>
          )}
        </div>
      )}

      {/* Create Button */}
      <div className="create-section">
        <button
          className="create-button"
          onClick={handleCreateAlias}
          disabled={!canCreateAlias}
        >
          Create Alias
        </button>
        
        {selectedSourceVariable && selectedTargetVariable && selectedMode && (
          <p className="alias-preview">
            {selectedSourceVariable.name} → {selectedTargetVariable.name} ({availableModes.find(m => m.modeId === selectedMode)?.name})
          </p>
        )}
      </div>
    </div>
  )
}
