# Augment UI - Design Token Exporter

A Figma plugin that exports Figma variables as design tokens using the [Design Tokens Format Module](https://tr.designtokens.org/format/) specification.

## Features

- 🎨 **Export Figma Variables**: Convert all your Figma variables to standardized design tokens
- 📋 **Design Tokens Format Module**: Follows the W3C specification for design tokens
- 🎯 **Selective Export**: Choose which variable collections to include
- 🏗️ **Flexible Organization**: Group tokens by collection or flatten the structure
- 📝 **Rich Metadata**: Include descriptions and Figma-specific extensions
- 💾 **Multiple Output Options**: Copy to clipboard or download as JSON

## Supported Variable Types

The plugin automatically converts Figma variables to appropriate token types:

### Colors
- **Figma**: Color variables
- **Output**: `$type: "color"` with hex values

### Dimensions
- **Figma**: Float variables with dimension scopes (width/height, corner radius, gap)
- **Output**: `$type: "dimension"` with pixel values

### Typography
- **Figma**: Float variables for font sizes, string variables for font families
- **Output**: `$type: "fontSize"`, `$type: "fontFamily"`, `$type: "fontWeight"`

### Numbers
- **Figma**: Float variables without specific scopes
- **Output**: `$type: "number"` with numeric values

### Strings & Booleans
- **Figma**: String and boolean variables
- **Output**: `$type: "string"`, `$type: "boolean"`

## Usage

1. **Open the Plugin**: In Figma, go to Plugins → Augment UI - Design Token Exporter
2. **Select Collections**: Choose which variable collections to export
3. **Configure Options**: Set export preferences (grouping, descriptions, etc.)
4. **Export**: Click "Export Design Tokens" to generate the output
5. **Use the Tokens**: Copy to clipboard or download the JSON file

## Export Options

### Group by Collection
When enabled, tokens are organized by their variable collection:
```json
{
  "colors": {
    "primary": {
      "500": {
        "$type": "color",
        "$value": "#3b82f6"
      }
    }
  },
  "spacing": {
    "md": {
      "$type": "dimension",
      "$value": "16px"
    }
  }
}
```

When disabled, all tokens are flattened to the root level.

### Include Descriptions
Adds variable descriptions as `$description` properties:
```json
{
  "primary-500": {
    "$type": "color",
    "$value": "#3b82f6",
    "$description": "Primary brand color for buttons and links"
  }
}
```

### Include Figma Extensions
Adds Figma-specific metadata in `$extensions`:
```json
{
  "primary-500": {
    "$type": "color",
    "$value": "#3b82f6",
    "$extensions": {
      "figma": {
        "id": "VariableID:123",
        "key": "abc123def456",
        "scopes": ["ALL_FILLS"],
        "hiddenFromPublishing": false,
        "remote": false
      }
    }
  }
}
```

## Variable Naming

The plugin supports nested token structures using forward slashes in variable names:

- `colors/primary/500` → `colors.primary.500`
- `spacing/sm` → `spacing.sm`
- `typography/heading/large` → `typography.heading.large`

## Output Format

The exported tokens follow the [Design Tokens Format Module](https://tr.designtokens.org/format/) specification:

```json
{
  "colors": {
    "primary": {
      "50": {
        "$type": "color",
        "$value": "#eff6ff",
        "$description": "Lightest primary color"
      },
      "500": {
        "$type": "color", 
        "$value": "#3b82f6",
        "$description": "Primary brand color"
      }
    }
  },
  "spacing": {
    "xs": {
      "$type": "dimension",
      "$value": "4px"
    },
    "sm": {
      "$type": "dimension", 
      "$value": "8px"
    }
  },
  "typography": {
    "fontSize": {
      "sm": {
        "$type": "fontSize",
        "$value": "14px"
      }
    },
    "fontFamily": {
      "sans": {
        "$type": "fontFamily",
        "$value": "Inter"
      }
    }
  }
}
```

## Integration

The exported tokens can be used with:

- **Style Dictionary**: Transform tokens to platform-specific formats
- **CSS Custom Properties**: Generate CSS variables
- **Design System Tools**: Import into component libraries
- **Build Tools**: Integrate with Webpack, Vite, etc.
- **Documentation**: Generate design system documentation

## Development

```bash
# Install dependencies
npm install

# Start development
npm run dev

# Build the plugin
npm run build
```

## Installation in Figma

1. Build the plugin: `npm run build`
2. In Figma, go to Plugins → Development → Import plugin from manifest
3. Select the `manifest.json` file from the `dist` folder
4. The plugin will appear in your Plugins menu

## License

MIT License - see LICENSE file for details.
