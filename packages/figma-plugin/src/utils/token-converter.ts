import {
  DesignToken,
  ColorToken,
  DimensionToken,
  FontFamilyToken,
  FontWeightToken,
  FontSizeToken,
  NumberToken,
  DesignTokensFile,
  TokenGroup,
  FigmaVariableType,
  ExportConfig
} from '../types/design-tokens'

/**
 * Converts Figma variables to Design Tokens Format Module
 */
export class TokenConverter {
  private config: ExportConfig

  constructor(config: ExportConfig = {}) {
    this.config = {
      groupByCollection: true,
      includeDescription: true,
      includeExtensions: false,
      ...config
    }
  }

  /**
   * Convert Figma variables to design tokens
   */
  async convertVariables(): Promise<DesignTokensFile> {
    const collections = await figma.variables.getLocalVariableCollectionsAsync()
    const tokens: DesignTokensFile = {}

    for (const collection of collections) {
      // Skip collections if specified in config
      if (this.config.excludeCollections?.includes(collection.name)) {
        continue
      }
      if (this.config.includeCollections && !this.config.includeCollections.includes(collection.name)) {
        continue
      }

      const collectionTokens = await this.convertCollection(collection)
      
      if (this.config.groupByCollection) {
        tokens[this.sanitizeName(collection.name)] = collectionTokens
      } else {
        Object.assign(tokens, collectionTokens)
      }
    }

    return tokens
  }

  /**
   * Convert a single variable collection
   */
  private async convertCollection(collection: VariableCollection): Promise<TokenGroup> {
    const tokens: TokenGroup = {}
    
    for (const variableId of collection.variableIds) {
      const variable = await figma.variables.getVariableByIdAsync(variableId)
      if (!variable) continue

      // Skip variables if specified in config
      if (this.config.excludeVariables?.includes(variable.name)) {
        continue
      }
      if (this.config.includeVariables && !this.config.includeVariables.includes(variable.name)) {
        continue
      }

      const token = this.convertVariable(variable)
      if (token) {
        const tokenPath = this.getTokenPath(variable.name)
        this.setNestedToken(tokens, tokenPath, token)
      }
    }

    return tokens
  }

  /**
   * Convert a single Figma variable to a design token
   */
  private convertVariable(variable: Variable): DesignToken | null {
    const baseToken: Partial<DesignToken> = {}

    // Add description if available and enabled
    if (this.config.includeDescription && variable.description) {
      baseToken.$description = variable.description
    }

    // Add extensions if enabled
    if (this.config.includeExtensions) {
      baseToken.$extensions = {
        'figma': {
          id: variable.id,
          key: variable.key,
          scopes: variable.scopes,
          hiddenFromPublishing: variable.hiddenFromPublishing,
          remote: variable.remote
        }
      }
    }

    // Get the default mode value
    const defaultModeId = Object.keys(variable.valuesByMode)[0]
    const value = variable.valuesByMode[defaultModeId]

    switch (variable.resolvedType) {
      case 'COLOR':
        return this.convertColorVariable(value as RGBA, baseToken)
      
      case 'FLOAT':
        return this.convertFloatVariable(value as number, variable, baseToken)
      
      case 'STRING':
        return this.convertStringVariable(value as string, variable, baseToken)
      
      case 'BOOLEAN':
        return this.convertBooleanVariable(value as boolean, baseToken)
      
      default:
        console.warn(`Unsupported variable type: ${variable.resolvedType}`)
        return null
    }
  }

  /**
   * Convert color variable
   */
  private convertColorVariable(rgba: RGBA, baseToken: Partial<DesignToken>): ColorToken {
    const hex = this.rgbaToHex(rgba)
    return {
      ...baseToken,
      $type: 'color',
      $value: hex
    } as ColorToken
  }

  /**
   * Convert float variable
   */
  private convertFloatVariable(value: number, variable: Variable, baseToken: Partial<DesignToken>): DesignToken {
    // Determine token type based on variable scopes
    const scopes = variable.scopes

    if (scopes.includes('CORNER_RADIUS') || scopes.includes('WIDTH_HEIGHT') || scopes.includes('GAP')) {
      return {
        ...baseToken,
        $type: 'dimension',
        $value: `${value}px`
      } as DimensionToken
    }

    if (scopes.includes('TEXT_CONTENT')) {
      // Could be font size, line height, etc.
      if (variable.name.toLowerCase().includes('size')) {
        return {
          ...baseToken,
          $type: 'fontSize',
          $value: `${value}px`
        } as FontSizeToken
      }
      
      if (variable.name.toLowerCase().includes('weight')) {
        return {
          ...baseToken,
          $type: 'fontWeight',
          $value: value
        } as FontWeightToken
      }
    }

    // Default to number token
    return {
      ...baseToken,
      $type: 'number',
      $value: value
    } as NumberToken
  }

  /**
   * Convert string variable
   */
  private convertStringVariable(value: string, variable: Variable, baseToken: Partial<DesignToken>): DesignToken {
    const scopes = variable.scopes

    if (scopes.includes('TEXT_CONTENT')) {
      // Could be font family
      if (variable.name.toLowerCase().includes('family') || variable.name.toLowerCase().includes('font')) {
        return {
          ...baseToken,
          $type: 'fontFamily',
          $value: value
        } as FontFamilyToken
      }
    }

    // Default to string token (not in spec, but useful)
    return {
      ...baseToken,
      $type: 'string',
      $value: value
    } as DesignToken
  }

  /**
   * Convert boolean variable
   */
  private convertBooleanVariable(value: boolean, baseToken: Partial<DesignToken>): DesignToken {
    return {
      ...baseToken,
      $type: 'boolean',
      $value: value
    } as DesignToken
  }

  /**
   * Convert RGBA to hex color
   */
  private rgbaToHex(rgba: RGBA): string {
    const toHex = (value: number) => {
      const hex = Math.round(value * 255).toString(16)
      return hex.length === 1 ? '0' + hex : hex
    }

    const hex = `#${toHex(rgba.r)}${toHex(rgba.g)}${toHex(rgba.b)}`
    
    // Include alpha if not fully opaque
    if (rgba.a < 1) {
      return `${hex}${toHex(rgba.a)}`
    }
    
    return hex
  }

  /**
   * Get token path from variable name (supports nested naming with /)
   */
  private getTokenPath(name: string): string[] {
    return name.split('/').map(part => this.sanitizeName(part))
  }

  /**
   * Set nested token in token group
   */
  private setNestedToken(tokens: TokenGroup, path: string[], token: DesignToken): void {
    let current = tokens
    
    for (let i = 0; i < path.length - 1; i++) {
      const key = path[i]
      if (!current[key] || typeof current[key] !== 'object' || '$value' in current[key]) {
        current[key] = {}
      }
      current = current[key] as TokenGroup
    }
    
    current[path[path.length - 1]] = token
  }

  /**
   * Sanitize name for use as token key
   */
  private sanitizeName(name: string): string {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9]/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '')
  }
}
