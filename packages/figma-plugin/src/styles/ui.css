/* Reset and base styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: 12px;
  line-height: 1.4;
  color: #333;
  background: #fff;
}

/* Plugin container */
.plugin-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  max-height: 600px;
}

/* Header */
.plugin-header {
  padding: 16px;
  border-bottom: 1px solid #e5e5e5;
  background: #f8f9fa;
}

.plugin-header h1 {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 4px;
  color: #1a1a1a;
}

.plugin-header p {
  font-size: 11px;
  color: #666;
  line-height: 1.3;
}

/* Content */
.plugin-content {
  flex: 1;
  overflow-y: auto;
  padding: 0;
}

/* Sections */
.section {
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.section:last-child {
  border-bottom: none;
}

.section h2 {
  font-size: 13px;
  font-weight: 600;
  margin-bottom: 4px;
  color: #1a1a1a;
}

.section-description {
  font-size: 11px;
  color: #666;
  margin-bottom: 12px;
  line-height: 1.3;
}

/* Collection selector */
.collection-summary {
  background: #f8f9fa;
  padding: 8px 12px;
  border-radius: 4px;
  margin-bottom: 12px;
}

.collection-summary p {
  font-size: 11px;
  color: #666;
  margin-bottom: 2px;
}

.collection-summary p:last-child {
  margin-bottom: 0;
}

.collection-actions {
  display: flex;
  gap: 8px;
  margin-bottom: 12px;
}

.action-button {
  padding: 4px 8px;
  font-size: 11px;
  border: 1px solid #ddd;
  border-radius: 3px;
  background: #fff;
  color: #333;
  cursor: pointer;
  transition: all 0.2s;
}

.action-button:hover {
  background: #f5f5f5;
  border-color: #ccc;
}

.action-button.primary {
  background: #0066cc;
  color: white;
  border-color: #0066cc;
}

.action-button.primary:hover {
  background: #0052a3;
  border-color: #0052a3;
}

.collection-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.collection-item {
  border: 1px solid #e5e5e5;
  border-radius: 4px;
  overflow: hidden;
}

.collection-label {
  display: flex;
  align-items: flex-start;
  padding: 8px;
  cursor: pointer;
  gap: 8px;
}

.collection-label:hover {
  background: #f8f9fa;
}

.collection-label input[type="checkbox"] {
  margin-top: 2px;
  flex-shrink: 0;
}

.collection-info {
  flex: 1;
  min-width: 0;
}

.collection-name {
  font-size: 12px;
  font-weight: 500;
  color: #1a1a1a;
  margin-bottom: 2px;
}

.collection-details {
  font-size: 10px;
  color: #666;
  margin-bottom: 4px;
}

.collection-description {
  font-size: 10px;
  color: #888;
  line-height: 1.3;
}

/* Export options */
.options-grid {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 16px;
}

.option-item {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  cursor: pointer;
  padding: 8px;
  border: 1px solid #e5e5e5;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.option-item:hover {
  background: #f8f9fa;
}

.option-item input[type="checkbox"] {
  margin-top: 2px;
  flex-shrink: 0;
}

.option-content {
  flex: 1;
}

.option-title {
  font-size: 12px;
  font-weight: 500;
  color: #1a1a1a;
  margin-bottom: 2px;
}

.option-description {
  font-size: 10px;
  color: #666;
  line-height: 1.3;
}

/* Format info */
.format-info {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.format-info h3 {
  font-size: 12px;
  font-weight: 600;
  margin-bottom: 8px;
  color: #1a1a1a;
}

.format-info p {
  font-size: 11px;
  color: #666;
  line-height: 1.3;
  margin-bottom: 12px;
}

.format-info a {
  color: #0066cc;
  text-decoration: none;
}

.format-info a:hover {
  text-decoration: underline;
}

.format-example {
  background: #f8f9fa;
  border-radius: 4px;
  padding: 12px;
}

.format-example h4 {
  font-size: 11px;
  font-weight: 600;
  margin-bottom: 8px;
  color: #1a1a1a;
}

.format-example pre {
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  font-size: 10px;
  line-height: 1.4;
  color: #333;
  overflow-x: auto;
  white-space: pre-wrap;
}

/* Export section */
.export-section {
  margin-top: 16px;
}

.export-button {
  width: 100%;
  padding: 12px;
  font-size: 12px;
  font-weight: 500;
  background: #0066cc;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
  margin-bottom: 12px;
}

.export-button:hover:not(:disabled) {
  background: #0052a3;
}

.export-button:disabled {
  background: #ccc;
  cursor: not-allowed;
}

/* Progress and error states */
.progress {
  padding: 8px 12px;
  background: #e3f2fd;
  border: 1px solid #bbdefb;
  border-radius: 4px;
  margin-bottom: 12px;
}

.progress p {
  font-size: 11px;
  color: #1565c0;
  margin: 0;
}

.error {
  padding: 8px 12px;
  background: #ffebee;
  border: 1px solid #ffcdd2;
  border-radius: 4px;
  margin-bottom: 12px;
}

.error p {
  font-size: 11px;
  color: #c62828;
  margin: 0;
}

.loading {
  padding: 24px;
  text-align: center;
}

.loading p {
  font-size: 12px;
  color: #666;
}

/* Results */
.results-summary {
  background: #e8f5e8;
  border: 1px solid #c8e6c9;
  border-radius: 4px;
  padding: 8px 12px;
  margin-bottom: 12px;
}

.results-summary p {
  font-size: 11px;
  color: #2e7d32;
  margin: 0;
}

.results-actions {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
}

.results-preview {
  margin-bottom: 16px;
}

.results-preview h3 {
  font-size: 12px;
  font-weight: 600;
  margin-bottom: 8px;
  color: #1a1a1a;
}

.code-preview {
  background: #f8f9fa;
  border: 1px solid #e5e5e5;
  border-radius: 4px;
  max-height: 200px;
  overflow: auto;
}

.code-preview pre {
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  font-size: 10px;
  line-height: 1.4;
  color: #333;
  padding: 12px;
  margin: 0;
  white-space: pre-wrap;
}

.usage-info h3 {
  font-size: 12px;
  font-weight: 600;
  margin-bottom: 8px;
  color: #1a1a1a;
}

.usage-info p {
  font-size: 11px;
  color: #666;
  line-height: 1.3;
  margin-bottom: 8px;
}

.usage-info ul {
  font-size: 11px;
  color: #666;
  line-height: 1.3;
  padding-left: 16px;
}

.usage-info li {
  margin-bottom: 4px;
}

/* Footer */
.plugin-footer {
  padding: 12px 16px;
  border-top: 1px solid #e5e5e5;
  background: #f8f9fa;
}

.close-button {
  width: 100%;
  padding: 8px;
  font-size: 11px;
  background: #fff;
  color: #666;
  border: 1px solid #ddd;
  border-radius: 3px;
  cursor: pointer;
  transition: all 0.2s;
}

.close-button:hover {
  background: #f5f5f5;
  border-color: #ccc;
}
