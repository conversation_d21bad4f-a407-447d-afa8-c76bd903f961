/**
 * Design Tokens Format Module types
 * Based on https://tr.designtokens.org/format/
 */

// Base token interface
export interface DesignToken {
  $value: any
  $type?: string
  $description?: string
  $extensions?: Record<string, any>
}

// Specific token types
export interface ColorToken extends DesignToken {
  $type: 'color'
  $value: string
}

export interface DimensionToken extends DesignToken {
  $type: 'dimension'
  $value: string
}

export interface FontFamilyToken extends DesignToken {
  $type: 'fontFamily'
  $value: string | string[]
}

export interface FontWeightToken extends DesignToken {
  $type: 'fontWeight'
  $value: string | number
}

export interface FontSizeToken extends DesignToken {
  $type: 'fontSize'
  $value: string
}

export interface LineHeightToken extends DesignToken {
  $type: 'lineHeight'
  $value: string | number
}

export interface LetterSpacingToken extends DesignToken {
  $type: 'letterSpacing'
  $value: string
}

export interface ParagraphSpacingToken extends DesignToken {
  $type: 'paragraphSpacing'
  $value: string
}

export interface TextDecorationToken extends DesignToken {
  $type: 'textDecoration'
  $value: 'none' | 'underline' | 'overline' | 'line-through'
}

export interface TextCaseToken extends DesignToken {
  $type: 'textCase'
  $value: 'none' | 'uppercase' | 'lowercase' | 'capitalize'
}

export interface ShadowToken extends DesignToken {
  $type: 'shadow'
  $value: {
    color: string
    offsetX: string
    offsetY: string
    blur: string
    spread: string
    inset?: boolean
  } | Array<{
    color: string
    offsetX: string
    offsetY: string
    blur: string
    spread: string
    inset?: boolean
  }>
}

export interface BorderToken extends DesignToken {
  $type: 'border'
  $value: {
    color: string
    width: string
    style: string
  }
}

export interface DurationToken extends DesignToken {
  $type: 'duration'
  $value: string
}

export interface CubicBezierToken extends DesignToken {
  $type: 'cubicBezier'
  $value: [number, number, number, number]
}

export interface NumberToken extends DesignToken {
  $type: 'number'
  $value: number
}

// Token group interface
export interface TokenGroup {
  [key: string]: DesignToken | TokenGroup
}

// Root design tokens file interface
export interface DesignTokensFile {
  [key: string]: DesignToken | TokenGroup
}

// Figma variable types mapping
export type FigmaVariableType = 'COLOR' | 'FLOAT' | 'STRING' | 'BOOLEAN'

// Figma variable scope mapping
export type FigmaVariableScope = 
  | 'ALL_SCOPES'
  | 'TEXT_CONTENT'
  | 'CORNER_RADIUS'
  | 'WIDTH_HEIGHT'
  | 'GAP'
  | 'ALL_FILLS'
  | 'FRAME_FILL'
  | 'SHAPE_FILL'
  | 'TEXT_FILL'
  | 'STROKE_COLOR'
  | 'EFFECT_COLOR'

// Export configuration
export interface ExportConfig {
  includeCollections?: string[]
  excludeCollections?: string[]
  includeVariables?: string[]
  excludeVariables?: string[]
  groupByCollection?: boolean
  includeDescription?: boolean
  includeExtensions?: boolean
}
