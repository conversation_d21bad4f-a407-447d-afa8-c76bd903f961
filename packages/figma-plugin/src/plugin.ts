import { TokenConverter } from './utils/token-converter'
import { ExportConfig } from './types/design-tokens'

// Show the plugin UI
figma.showUI(__html__, {
  width: 400,
  height: 600,
  title: 'Design Token Exporter'
})

// Handle messages from the UI
figma.ui.onmessage = async (msg) => {
  switch (msg.type) {
    case 'export-tokens':
      await handleExportTokens(msg.config)
      break
    
    case 'get-collections':
      await handleGetCollections()
      break
    
    case 'close':
      figma.closePlugin()
      break
    
    default:
      console.warn('Unknown message type:', msg.type)
  }
}

/**
 * Handle exporting design tokens
 */
async function handleExportTokens(config: ExportConfig) {
  try {
    figma.ui.postMessage({
      type: 'export-progress',
      message: 'Starting export...'
    })

    // Check if there are any variables
    const collections = await figma.variables.getLocalVariableCollectionsAsync()
    
    if (collections.length === 0) {
      figma.ui.postMessage({
        type: 'export-error',
        message: 'No variable collections found in this file.'
      })
      return
    }

    figma.ui.postMessage({
      type: 'export-progress',
      message: 'Converting variables to design tokens...'
    })

    // Convert variables to design tokens
    const converter = new TokenConverter(config)
    const tokens = await converter.convertVariables()

    // Check if any tokens were generated
    if (Object.keys(tokens).length === 0) {
      figma.ui.postMessage({
        type: 'export-error',
        message: 'No tokens were generated. Check your filter settings.'
      })
      return
    }

    figma.ui.postMessage({
      type: 'export-progress',
      message: 'Formatting output...'
    })

    // Format the output
    const output = JSON.stringify(tokens, null, 2)
    
    // Send the result back to the UI
    figma.ui.postMessage({
      type: 'export-complete',
      tokens: output,
      tokenCount: countTokens(tokens)
    })

    // Show success notification
    figma.notify('Design tokens exported successfully!')

  } catch (error) {
    console.error('Export error:', error)
    figma.ui.postMessage({
      type: 'export-error',
      message: error instanceof Error ? error.message : 'An unknown error occurred'
    })
    figma.notify('Export failed. Check the console for details.', { error: true })
  }
}

/**
 * Handle getting available collections
 */
async function handleGetCollections() {
  try {
    const collections = await figma.variables.getLocalVariableCollectionsAsync()
    
    const collectionData = collections.map(collection => ({
      id: collection.id,
      name: collection.name,
      description: collection.description,
      variableCount: collection.variableIds.length,
      modes: collection.modes.map(mode => ({
        modeId: mode.modeId,
        name: mode.name
      }))
    }))

    figma.ui.postMessage({
      type: 'collections-data',
      collections: collectionData
    })

  } catch (error) {
    console.error('Error getting collections:', error)
    figma.ui.postMessage({
      type: 'export-error',
      message: 'Failed to load variable collections'
    })
  }
}

/**
 * Count the total number of tokens in the output
 */
function countTokens(obj: any): number {
  let count = 0
  
  for (const key in obj) {
    if (obj[key] && typeof obj[key] === 'object') {
      if ('$value' in obj[key]) {
        count++
      } else {
        count += countTokens(obj[key])
      }
    }
  }
  
  return count
}

// Initialize the plugin
async function init() {
  // Load collections on startup
  await handleGetCollections()
}
