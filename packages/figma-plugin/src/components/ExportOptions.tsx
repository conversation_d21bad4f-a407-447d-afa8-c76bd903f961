import React from 'react'
import { ExportConfig } from '../types/design-tokens'

interface ExportOptionsProps {
  config: ExportConfig
  onConfigChange: (config: Partial<ExportConfig>) => void
}

export const ExportOptions: React.FC<ExportOptionsProps> = ({
  config,
  onConfigChange
}) => {
  return (
    <div className="section">
      <h2>Export Options</h2>
      <p className="section-description">
        Configure how the design tokens should be formatted and organized.
      </p>

      <div className="options-grid">
        <label className="option-item">
          <input
            type="checkbox"
            checked={config.groupByCollection ?? true}
            onChange={(e) => onConfigChange({ groupByCollection: e.target.checked })}
          />
          <div className="option-content">
            <div className="option-title">Group by Collection</div>
            <div className="option-description">
              Organize tokens into groups based on their variable collection
            </div>
          </div>
        </label>

        <label className="option-item">
          <input
            type="checkbox"
            checked={config.includeDescription ?? true}
            onChange={(e) => onConfigChange({ includeDescription: e.target.checked })}
          />
          <div className="option-content">
            <div className="option-title">Include Descriptions</div>
            <div className="option-description">
              Add variable descriptions as $description properties
            </div>
          </div>
        </label>

        <label className="option-item">
          <input
            type="checkbox"
            checked={config.includeExtensions ?? false}
            onChange={(e) => onConfigChange({ includeExtensions: e.target.checked })}
          />
          <div className="option-content">
            <div className="option-title">Include Figma Extensions</div>
            <div className="option-description">
              Add Figma-specific metadata like variable IDs and scopes
            </div>
          </div>
        </label>
      </div>

      <div className="format-info">
        <h3>Output Format</h3>
        <p>
          Tokens will be exported in the{' '}
          <a 
            href="https://tr.designtokens.org/format/" 
            target="_blank" 
            rel="noopener noreferrer"
          >
            Design Tokens Format Module
          </a>{' '}
          specification format.
        </p>
        
        <div className="format-example">
          <h4>Example Output:</h4>
          <pre>{`{
  "colors": {
    "primary": {
      "500": {
        "$type": "color",
        "$value": "#3b82f6",
        "$description": "Primary brand color"
      }
    }
  },
  "spacing": {
    "md": {
      "$type": "dimension", 
      "$value": "16px"
    }
  }
}`}</pre>
        </div>
      </div>
    </div>
  )
}
