import React, { useState, useEffect } from 'react'
import { ExportConfig } from '../types/design-tokens'
import { CollectionSelector } from './CollectionSelector'
import { ExportOptions } from './ExportOptions'
import { ExportResults } from './ExportResults'

interface Collection {
  id: string
  name: string
  description: string
  variableCount: number
  modes: Array<{
    modeId: string
    name: string
  }>
}

interface AppState {
  collections: Collection[]
  config: ExportConfig
  isExporting: boolean
  exportResult: string | null
  exportError: string | null
  exportProgress: string | null
  tokenCount: number
}

export const App: React.FC = () => {
  const [state, setState] = useState<AppState>({
    collections: [],
    config: {
      groupByCollection: true,
      includeDescription: true,
      includeExtensions: false
    },
    isExporting: false,
    exportResult: null,
    exportError: null,
    exportProgress: null,
    tokenCount: 0
  })

  useEffect(() => {
    // Listen for messages from the plugin
    window.onmessage = (event) => {
      const { type, ...data } = event.data.pluginMessage

      switch (type) {
        case 'collections-data':
          setState(prev => ({
            ...prev,
            collections: data.collections
          }))
          break

        case 'export-progress':
          setState(prev => ({
            ...prev,
            exportProgress: data.message,
            exportError: null
          }))
          break

        case 'export-complete':
          setState(prev => ({
            ...prev,
            isExporting: false,
            exportResult: data.tokens,
            exportProgress: null,
            exportError: null,
            tokenCount: data.tokenCount
          }))
          break

        case 'export-error':
          setState(prev => ({
            ...prev,
            isExporting: false,
            exportProgress: null,
            exportError: data.message
          }))
          break
      }
    }

    // Request collections data on mount
    parent.postMessage({
      pluginMessage: { type: 'get-collections' }
    }, '*')
  }, [])

  const handleExport = () => {
    setState(prev => ({
      ...prev,
      isExporting: true,
      exportResult: null,
      exportError: null,
      exportProgress: 'Preparing export...'
    }))

    parent.postMessage({
      pluginMessage: {
        type: 'export-tokens',
        config: state.config
      }
    }, '*')
  }

  const handleConfigChange = (newConfig: Partial<ExportConfig>) => {
    setState(prev => ({
      ...prev,
      config: { ...prev.config, ...newConfig }
    }))
  }

  const handleClose = () => {
    parent.postMessage({
      pluginMessage: { type: 'close' }
    }, '*')
  }

  return (
    <div className="plugin-container">
      <div className="plugin-header">
        <h1>Design Token Exporter</h1>
        <p>Export Figma variables as design tokens using the Design Tokens Format Module</p>
      </div>

      <div className="plugin-content">
        {state.collections.length === 0 ? (
          <div className="loading">
            <p>Loading variable collections...</p>
          </div>
        ) : (
          <>
            <CollectionSelector
              collections={state.collections}
              config={state.config}
              onConfigChange={handleConfigChange}
            />

            <ExportOptions
              config={state.config}
              onConfigChange={handleConfigChange}
            />

            <div className="export-section">
              <button
                className="export-button"
                onClick={handleExport}
                disabled={state.isExporting}
              >
                {state.isExporting ? 'Exporting...' : 'Export Design Tokens'}
              </button>

              {state.exportProgress && (
                <div className="progress">
                  <p>{state.exportProgress}</p>
                </div>
              )}

              {state.exportError && (
                <div className="error">
                  <p>{state.exportError}</p>
                </div>
              )}

              {state.exportResult && (
                <ExportResults
                  result={state.exportResult}
                  tokenCount={state.tokenCount}
                />
              )}
            </div>
          </>
        )}
      </div>

      <div className="plugin-footer">
        <button className="close-button" onClick={handleClose}>
          Close
        </button>
      </div>
    </div>
  )
}
