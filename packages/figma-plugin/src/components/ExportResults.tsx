import React, { useState } from 'react'

interface ExportResultsProps {
  result: string
  tokenCount: number
}

export const ExportResults: React.FC<ExportResultsProps> = ({
  result,
  tokenCount
}) => {
  const [copied, setCopied] = useState(false)

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(result)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (error) {
      console.error('Failed to copy to clipboard:', error)
      // Fallback for older browsers
      const textArea = document.createElement('textarea')
      textArea.value = result
      document.body.appendChild(textArea)
      textArea.select()
      document.execCommand('copy')
      document.body.removeChild(textArea)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    }
  }

  const handleDownload = () => {
    const blob = new Blob([result], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = 'design-tokens.json'
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  return (
    <div className="section">
      <h2>Export Results</h2>
      
      <div className="results-summary">
        <p>
          Successfully exported <strong>{tokenCount}</strong> design tokens
        </p>
      </div>

      <div className="results-actions">
        <button 
          className="action-button primary"
          onClick={handleCopy}
        >
          {copied ? 'Copied!' : 'Copy to Clipboard'}
        </button>
        <button 
          className="action-button"
          onClick={handleDownload}
        >
          Download JSON
        </button>
      </div>

      <div className="results-preview">
        <h3>Preview</h3>
        <div className="code-preview">
          <pre>{result}</pre>
        </div>
      </div>

      <div className="usage-info">
        <h3>Usage</h3>
        <p>
          You can now use these design tokens in your design system or development workflow:
        </p>
        <ul>
          <li>Import into design token tools like Style Dictionary</li>
          <li>Use with CSS-in-JS libraries</li>
          <li>Generate platform-specific formats (CSS, SCSS, JavaScript, etc.)</li>
          <li>Integrate with your component library</li>
        </ul>
      </div>
    </div>
  )
}
