import React from 'react'
import { ExportConfig } from '../types/design-tokens'

interface Collection {
  id: string
  name: string
  description: string
  variableCount: number
  modes: Array<{
    modeId: string
    name: string
  }>
}

interface CollectionSelectorProps {
  collections: Collection[]
  config: ExportConfig
  onConfigChange: (config: Partial<ExportConfig>) => void
}

export const CollectionSelector: React.FC<CollectionSelectorProps> = ({
  collections,
  config,
  onConfigChange
}) => {
  const handleCollectionToggle = (collectionName: string, include: boolean) => {
    const includeCollections = config.includeCollections || []
    
    if (include) {
      // Add to include list
      const newInclude = [...includeCollections, collectionName]
      onConfigChange({ includeCollections: newInclude })
    } else {
      // Remove from include list
      const newInclude = includeCollections.filter(name => name !== collectionName)
      onConfigChange({ 
        includeCollections: newInclude.length > 0 ? newInclude : undefined 
      })
    }
  }

  const handleSelectAll = () => {
    onConfigChange({ includeCollections: undefined })
  }

  const handleSelectNone = () => {
    onConfigChange({ includeCollections: [] })
  }

  const isCollectionIncluded = (collectionName: string): boolean => {
    if (!config.includeCollections) return true
    return config.includeCollections.includes(collectionName)
  }

  const totalVariables = collections.reduce((sum, col) => sum + col.variableCount, 0)
  const selectedVariables = collections
    .filter(col => isCollectionIncluded(col.name))
    .reduce((sum, col) => sum + col.variableCount, 0)

  return (
    <div className="section">
      <h2>Variable Collections</h2>
      <p className="section-description">
        Select which variable collections to include in the export.
      </p>

      <div className="collection-summary">
        <p>
          <strong>{collections.length}</strong> collections found with{' '}
          <strong>{totalVariables}</strong> total variables
        </p>
        <p>
          <strong>{selectedVariables}</strong> variables selected for export
        </p>
      </div>

      <div className="collection-actions">
        <button 
          className="action-button"
          onClick={handleSelectAll}
        >
          Select All
        </button>
        <button 
          className="action-button"
          onClick={handleSelectNone}
        >
          Select None
        </button>
      </div>

      <div className="collection-list">
        {collections.map(collection => (
          <div key={collection.id} className="collection-item">
            <label className="collection-label">
              <input
                type="checkbox"
                checked={isCollectionIncluded(collection.name)}
                onChange={(e) => handleCollectionToggle(collection.name, e.target.checked)}
              />
              <div className="collection-info">
                <div className="collection-name">{collection.name}</div>
                <div className="collection-details">
                  {collection.variableCount} variables
                  {collection.modes.length > 1 && (
                    <span> • {collection.modes.length} modes</span>
                  )}
                </div>
                {collection.description && (
                  <div className="collection-description">
                    {collection.description}
                  </div>
                )}
              </div>
            </label>
          </div>
        ))}
      </div>
    </div>
  )
}
