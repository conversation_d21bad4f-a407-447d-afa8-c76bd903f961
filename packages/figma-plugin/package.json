{"name": "@augment/figma-plugin", "version": "0.1.0", "description": "Figma plugin to export variables as design tokens using the Design Tokens Format Module", "type": "module", "private": true, "scripts": {"dev": "vite", "build": "npm run build:ui && npm run build:plugin", "build:ui": "vite build -c ./vite.config.ui.ts", "build:plugin": "vite build -c ./vite.config.plugin.ts", "preview": "vite preview", "typecheck": "tsc --noEmit", "lint": "echo \"No linting configured yet\"", "test": "echo \"No tests configured yet\""}, "dependencies": {"react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@figma/plugin-typings": "^1.98.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "typescript": "^5.8.3", "vite": "^5.4.19", "vite-plugin-generate-file": "^0.3.1", "vite-plugin-singlefile": "^2.2.0"}}