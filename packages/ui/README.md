# @augment/ui

A flexible React component library built with design tokens, Tailwind CSS, and TypeScript.

## Installation

```bash
npm install @augment/ui
# or
yarn add @augment/ui
# or
pnpm add @augment/ui
```

## Quick Start

1. Wrap your app with the `ThemeProvider`:

```tsx
import { ThemeProvider } from '@augment/ui'
import '@augment/ui/styles'

function App() {
  return (
    <ThemeProvider>
      {/* Your app content */}
    </ThemeProvider>
  )
}
```

2. Start using components:

```tsx
import { Button, Stack, Heading, Text } from '@augment/ui'

function MyComponent() {
  return (
    <Stack spacing={4}>
      <Heading>Welcome to Augment UI</Heading>
      <Text>A modern React component library</Text>
      <Button variant="primary">Get Started</Button>
    </Stack>
  )
}
```

## Components

### Layout
- `Box` - The fundamental layout component
- `Flex` - Flexbox container with convenient props
- `Stack` / `HStack` - Vertical and horizontal stacks
- `Grid` / `GridItem` - CSS Grid layouts
- `Container` - Responsive container with max-width
- `Divider` - Visual content separator
- `Card` - Content container with variants
- `Spacer` - Flexible spacing component
- `Center` - Center content horizontally and vertically

### Typography
- `Text` - Text component with variants
- `Heading` - Semantic headings (h1-h6)
- `Code` - Inline and block code display
- `Link` - Styled links with external link support

### Forms
- `Button` - Interactive buttons with variants
- `Input` - Text input with icons and validation states
- `Textarea` - Multi-line text input
- `Checkbox` - Checkbox input with labels
- `Radio` / `RadioGroup` - Radio button inputs
- `Label` - Form field labels
- `FormField` - Complete form field with label, input, and error handling

### Feedback
- `Alert` - Alert messages with variants
- `Badge` - Status and label badges
- `Spinner` - Loading spinners
- `Progress` - Progress bars and indicators
- `Skeleton` - Loading placeholders

## Development

This package is part of a monorepo. To develop:

```bash
# From the root directory
npm install
npm run dev:ui  # Start in watch mode
npm run build   # Build the library
```

## License

MIT License - see LICENSE file for details.
