{"name": "@augment/ui", "version": "0.1.0", "description": "A flexible React component library with design tokens, Tailwind CSS, and TypeScript", "type": "module", "main": "./dist/augment-ui.umd.js", "module": "./dist/augment-ui.es.js", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/augment-ui.es.js", "require": "./dist/augment-ui.umd.js"}, "./styles": "./dist/style.css"}, "files": ["dist"], "scripts": {"dev": "vite build --watch", "build": "npm run build:types && vite build", "build:types": "tsc -p tsconfig.build.json", "clean": "rm -rf dist", "typecheck": "tsc --noEmit", "lint": "echo \"No linting configured yet\"", "test": "echo \"No tests configured yet\""}, "keywords": ["react", "component-library", "design-system", "tailwindcss", "typescript", "ui"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dependencies": {"@tailwindcss/forms": "^0.5.10", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "tailwindcss": "^4.1.11"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.11", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "autoprefixer": "^10.4.21", "postcss": "^8.5.6", "react": "^19.1.0", "react-dom": "^19.1.0", "rollup-plugin-dts": "^6.2.1", "vite": "^5.4.19"}}