import React, { createContext, useContext, useMemo } from 'react'
import { Theme, defaultTheme, semanticTokens } from './theme'
import { generateCSSVars } from './css-vars'

export interface ThemeContextValue {
  theme: Theme
  semanticTokens: typeof semanticTokens
}

const ThemeContext = createContext<ThemeContextValue | undefined>(undefined)

export interface ThemeProviderProps {
  children: React.ReactNode
  theme?: Partial<Theme>
  colorMode?: 'light' | 'dark'
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({
  children,
  theme: customTheme,
  colorMode = 'light',
}) => {
  const theme = useMemo(() => {
    if (!customTheme) return defaultTheme
    
    // Deep merge custom theme with default theme
    return {
      ...defaultTheme,
      ...customTheme,
      colors: {
        ...defaultTheme.colors,
        ...customTheme.colors,
      },
      spacing: {
        ...defaultTheme.spacing,
        ...customTheme.spacing,
      },
      typography: {
        ...defaultTheme.typography,
        ...customTheme.typography,
        fontSize: {
          ...defaultTheme.typography.fontSize,
          ...customTheme.typography?.fontSize,
        },
        fontWeight: {
          ...defaultTheme.typography.fontWeight,
          ...customTheme.typography?.fontWeight,
        },
        fontFamily: {
          ...defaultTheme.typography.fontFamily,
          ...customTheme.typography?.fontFamily,
        },
      },
      borderRadius: {
        ...defaultTheme.borderRadius,
        ...customTheme.borderRadius,
      },
      shadows: {
        ...defaultTheme.shadows,
        ...customTheme.shadows,
      },
      zIndex: {
        ...defaultTheme.zIndex,
        ...customTheme.zIndex,
      },
      breakpoints: {
        ...defaultTheme.breakpoints,
        ...customTheme.breakpoints,
      },
    }
  }, [customTheme])

  const contextValue = useMemo(() => ({
    theme,
    semanticTokens,
  }), [theme])

  // Generate CSS custom properties
  const cssVars = useMemo(() => generateCSSVars(theme), [theme])

  // Apply CSS variables to the root element
  React.useEffect(() => {
    const root = document.documentElement
    Object.entries(cssVars).forEach(([property, value]) => {
      root.style.setProperty(property, value)
    })

    // Set color mode class
    root.classList.remove('light', 'dark')
    root.classList.add(colorMode)

    return () => {
      // Cleanup on unmount
      Object.keys(cssVars).forEach((property) => {
        root.style.removeProperty(property)
      })
      root.classList.remove('light', 'dark')
    }
  }, [cssVars, colorMode])

  return (
    <ThemeContext.Provider value={contextValue}>
      <div className="augment-ui" data-theme={colorMode}>
        {children}
      </div>
    </ThemeContext.Provider>
  )
}

export const useTheme = (): ThemeContextValue => {
  const context = useContext(ThemeContext)
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider')
  }
  return context
}

// Hook to get a specific color value
export const useColorValue = (colorPath: string): string => {
  const { theme } = useTheme()
  const parts = colorPath.split('.')
  let value: any = theme.colors
  
  for (const part of parts) {
    value = value?.[part]
  }
  
  return typeof value === 'string' ? value : ''
}

// Hook to get spacing value
export const useSpacingValue = (spacing: keyof Theme['spacing']): string => {
  const { theme } = useTheme()
  return theme.spacing[spacing]
}

// Hook to get semantic token values
export const useSemanticTokens = () => {
  const { semanticTokens } = useTheme()
  return semanticTokens
}

// Higher-order component for theme injection
export function withTheme<P extends object>(
  Component: React.ComponentType<P & { theme: Theme }>
): React.ComponentType<P> {
  const WrappedComponent = (props: P) => {
    const { theme } = useTheme()
    return <Component {...props} theme={theme} />
  }
  
  WrappedComponent.displayName = `withTheme(${Component.displayName || Component.name})`
  return WrappedComponent
}
