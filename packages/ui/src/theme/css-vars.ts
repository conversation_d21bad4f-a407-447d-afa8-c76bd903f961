import { Theme, defaultTheme } from './theme'

// Generate CSS custom properties from theme tokens
export const generateCSSVars = (theme: Theme = defaultTheme): Record<string, string> => {
  const cssVars: Record<string, string> = {}

  // Colors
  Object.entries(theme.colors).forEach(([colorName, colorScale]) => {
    Object.entries(colorScale).forEach(([shade, value]) => {
      cssVars[`--color-${colorName}-${shade}`] = value
    })
  })

  // Spacing
  Object.entries(theme.spacing).forEach(([key, value]) => {
    cssVars[`--spacing-${key}`] = value
  })

  // Typography
  Object.entries(theme.typography.fontSize).forEach(([key, [size, { lineHeight }]]) => {
    cssVars[`--font-size-${key}`] = size
    cssVars[`--line-height-${key}`] = lineHeight
  })

  Object.entries(theme.typography.fontWeight).forEach(([key, value]) => {
    cssVars[`--font-weight-${key}`] = value
  })

  // Border radius
  Object.entries(theme.borderRadius).forEach(([key, value]) => {
    cssVars[`--border-radius-${key}`] = value
  })

  // Shadows
  Object.entries(theme.shadows).forEach(([key, value]) => {
    cssVars[`--shadow-${key}`] = value
  })

  // Z-index
  Object.entries(theme.zIndex).forEach(([key, value]) => {
    cssVars[`--z-index-${key}`] = value.toString()
  })

  return cssVars
}

// Convert CSS vars object to CSS string
export const cssVarsToString = (cssVars: Record<string, string>): string => {
  return Object.entries(cssVars)
    .map(([property, value]) => `  ${property}: ${value};`)
    .join('\n')
}

// Generate the complete CSS custom properties string
export const generateThemeCSS = (theme: Theme = defaultTheme): string => {
  const cssVars = generateCSSVars(theme)
  return `:root {\n${cssVarsToString(cssVars)}\n}`
}

// Utility to get CSS var reference
export const cssVar = (property: string): string => `var(--${property})`

// Predefined CSS var references for common tokens
export const cssVars = {
  // Colors
  color: {
    primary: {
      50: cssVar('color-primary-50'),
      100: cssVar('color-primary-100'),
      200: cssVar('color-primary-200'),
      300: cssVar('color-primary-300'),
      400: cssVar('color-primary-400'),
      500: cssVar('color-primary-500'),
      600: cssVar('color-primary-600'),
      700: cssVar('color-primary-700'),
      800: cssVar('color-primary-800'),
      900: cssVar('color-primary-900'),
      950: cssVar('color-primary-950'),
    },
    secondary: {
      50: cssVar('color-secondary-50'),
      100: cssVar('color-secondary-100'),
      200: cssVar('color-secondary-200'),
      300: cssVar('color-secondary-300'),
      400: cssVar('color-secondary-400'),
      500: cssVar('color-secondary-500'),
      600: cssVar('color-secondary-600'),
      700: cssVar('color-secondary-700'),
      800: cssVar('color-secondary-800'),
      900: cssVar('color-secondary-900'),
      950: cssVar('color-secondary-950'),
    },
    neutral: {
      50: cssVar('color-neutral-50'),
      100: cssVar('color-neutral-100'),
      200: cssVar('color-neutral-200'),
      300: cssVar('color-neutral-300'),
      400: cssVar('color-neutral-400'),
      500: cssVar('color-neutral-500'),
      600: cssVar('color-neutral-600'),
      700: cssVar('color-neutral-700'),
      800: cssVar('color-neutral-800'),
      900: cssVar('color-neutral-900'),
      950: cssVar('color-neutral-950'),
    },
  },
  // Spacing
  spacing: {
    0: cssVar('spacing-0'),
    1: cssVar('spacing-1'),
    2: cssVar('spacing-2'),
    3: cssVar('spacing-3'),
    4: cssVar('spacing-4'),
    5: cssVar('spacing-5'),
    6: cssVar('spacing-6'),
    8: cssVar('spacing-8'),
    10: cssVar('spacing-10'),
    12: cssVar('spacing-12'),
    16: cssVar('spacing-16'),
    20: cssVar('spacing-20'),
    24: cssVar('spacing-24'),
  },
  // Typography
  fontSize: {
    xs: cssVar('font-size-xs'),
    sm: cssVar('font-size-sm'),
    base: cssVar('font-size-base'),
    lg: cssVar('font-size-lg'),
    xl: cssVar('font-size-xl'),
    '2xl': cssVar('font-size-2xl'),
    '3xl': cssVar('font-size-3xl'),
    '4xl': cssVar('font-size-4xl'),
  },
  fontWeight: {
    normal: cssVar('font-weight-normal'),
    medium: cssVar('font-weight-medium'),
    semibold: cssVar('font-weight-semibold'),
    bold: cssVar('font-weight-bold'),
  },
  // Border radius
  borderRadius: {
    none: cssVar('border-radius-none'),
    sm: cssVar('border-radius-sm'),
    base: cssVar('border-radius-base'),
    md: cssVar('border-radius-md'),
    lg: cssVar('border-radius-lg'),
    xl: cssVar('border-radius-xl'),
    full: cssVar('border-radius-full'),
  },
  // Shadows
  shadow: {
    sm: cssVar('shadow-sm'),
    base: cssVar('shadow-base'),
    md: cssVar('shadow-md'),
    lg: cssVar('shadow-lg'),
    xl: cssVar('shadow-xl'),
  },
}
