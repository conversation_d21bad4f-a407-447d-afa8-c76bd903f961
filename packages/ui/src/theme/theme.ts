import { tokens } from './tokens'

export interface Theme {
  colors: typeof tokens.colors
  spacing: typeof tokens.spacing
  typography: typeof tokens.typography
  borderRadius: typeof tokens.borderRadius
  shadows: typeof tokens.shadows
  zIndex: typeof tokens.zIndex
  breakpoints: typeof tokens.breakpoints
}

export const defaultTheme: Theme = {
  colors: tokens.colors,
  spacing: tokens.spacing,
  typography: tokens.typography,
  borderRadius: tokens.borderRadius,
  shadows: tokens.shadows,
  zIndex: tokens.zIndex,
  breakpoints: tokens.breakpoints,
}

// Component-specific semantic tokens
export const semanticTokens = {
  colors: {
    // Background colors
    bg: {
      default: tokens.colors.neutral[50],
      subtle: tokens.colors.neutral[100],
      muted: tokens.colors.neutral[200],
      emphasis: tokens.colors.neutral[300],
      inverse: tokens.colors.neutral[900],
    },
    // Text colors
    text: {
      default: tokens.colors.neutral[900],
      muted: tokens.colors.neutral[600],
      subtle: tokens.colors.neutral[500],
      inverse: tokens.colors.neutral[50],
      disabled: tokens.colors.neutral[400],
    },
    // Border colors
    border: {
      default: tokens.colors.neutral[200],
      muted: tokens.colors.neutral[100],
      subtle: tokens.colors.neutral[50],
      emphasis: tokens.colors.neutral[300],
    },
    // Interactive colors
    interactive: {
      default: tokens.colors.primary[500],
      hover: tokens.colors.primary[600],
      active: tokens.colors.primary[700],
      disabled: tokens.colors.neutral[300],
    },
    // Status colors
    status: {
      success: tokens.colors.success[500],
      warning: tokens.colors.warning[500],
      error: tokens.colors.error[500],
      info: tokens.colors.primary[500],
    },
  },
  // Component sizes
  sizes: {
    xs: {
      height: tokens.spacing[6],
      padding: `${tokens.spacing[1]} ${tokens.spacing[2]}`,
      fontSize: tokens.typography.fontSize.xs[0],
    },
    sm: {
      height: tokens.spacing[8],
      padding: `${tokens.spacing[2]} ${tokens.spacing[3]}`,
      fontSize: tokens.typography.fontSize.sm[0],
    },
    md: {
      height: tokens.spacing[10],
      padding: `${tokens.spacing[2]} ${tokens.spacing[4]}`,
      fontSize: tokens.typography.fontSize.base[0],
    },
    lg: {
      height: tokens.spacing[12],
      padding: `${tokens.spacing[3]} ${tokens.spacing[6]}`,
      fontSize: tokens.typography.fontSize.lg[0],
    },
    xl: {
      height: tokens.spacing[14],
      padding: `${tokens.spacing[4]} ${tokens.spacing[8]}`,
      fontSize: tokens.typography.fontSize.xl[0],
    },
  },
} as const

export type SemanticTokens = typeof semanticTokens
export type ComponentSize = keyof typeof semanticTokens.sizes

// Theme utilities
export const getColorValue = (colorPath: string, theme: Theme = defaultTheme): string => {
  const parts = colorPath.split('.')
  let value: any = theme.colors
  
  for (const part of parts) {
    value = value?.[part]
  }
  
  return typeof value === 'string' ? value : ''
}

export const getSpacingValue = (spacing: keyof typeof tokens.spacing, theme: Theme = defaultTheme): string => {
  return theme.spacing[spacing]
}

export const getFontSizeValue = (fontSize: keyof typeof tokens.typography.fontSize, theme: Theme = defaultTheme): readonly [string, { readonly lineHeight: string }] => {
  return theme.typography.fontSize[fontSize]
}

export const getBorderRadiusValue = (radius: keyof typeof tokens.borderRadius, theme: Theme = defaultTheme): string => {
  return theme.borderRadius[radius]
}

export const getShadowValue = (shadow: keyof typeof tokens.shadows, theme: Theme = defaultTheme): string => {
  return theme.shadows[shadow]
}
