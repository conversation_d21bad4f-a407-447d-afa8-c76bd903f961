import React from 'react'
import { Flex, FlexProps } from './Flex'

export interface CenterProps extends Omit<FlexProps, 'justify' | 'align'> {
  /**
   * Whether to center only horizontally
   */
  inline?: boolean
}

/**
 * Center component for centering content both horizontally and vertically
 */
export const Center = React.forwardRef<HTMLDivElement, CenterProps>(
  ({ inline = false, ...props }, ref) => {
    return (
      <Flex
        ref={ref}
        justify="center"
        align="center"
        inline={inline}
        {...props}
      />
    )
  }
)

Center.displayName = 'Center'
