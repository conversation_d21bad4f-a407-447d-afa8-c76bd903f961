import React from 'react'
import { Box, BoxProps } from './Box'
import { cn } from '../../utils/cn'

export interface ContainerProps extends BoxProps {
  /**
   * Maximum width of the container
   */
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '3xl' | '4xl' | '5xl' | '6xl' | '7xl' | 'full'
  /**
   * Whether to center the container
   */
  centerContent?: boolean
  /**
   * Padding for the container
   */
  padding?: 0 | 1 | 2 | 3 | 4 | 5 | 6 | 8 | 10 | 12 | 16 | 20 | 24
}

/**
 * Container is a layout component that constrains content to a maximum width and centers it
 */
export const Container = React.forwardRef<HTMLDivElement, ContainerProps>(
  ({ 
    maxWidth = 'lg',
    centerContent = true,
    padding = 4,
    className,
    ...props 
  }, ref) => {
    const containerClasses = cn(
      'w-full',
      {
        // Max width
        'max-w-sm': maxWidth === 'sm',
        'max-w-md': maxWidth === 'md',
        'max-w-lg': maxWidth === 'lg',
        'max-w-xl': maxWidth === 'xl',
        'max-w-2xl': maxWidth === '2xl',
        'max-w-3xl': maxWidth === '3xl',
        'max-w-4xl': maxWidth === '4xl',
        'max-w-5xl': maxWidth === '5xl',
        'max-w-6xl': maxWidth === '6xl',
        'max-w-7xl': maxWidth === '7xl',
        'max-w-full': maxWidth === 'full',
        
        // Center content
        'mx-auto': centerContent,
        
        // Padding
        'p-0': padding === 0,
        'p-1': padding === 1,
        'p-2': padding === 2,
        'p-3': padding === 3,
        'p-4': padding === 4,
        'p-5': padding === 5,
        'p-6': padding === 6,
        'p-8': padding === 8,
        'p-10': padding === 10,
        'p-12': padding === 12,
        'p-16': padding === 16,
        'p-20': padding === 20,
        'p-24': padding === 24,
      },
      className
    )

    return (
      <Box
        ref={ref}
        className={containerClasses}
        {...props}
      />
    )
  }
)

Container.displayName = 'Container'
