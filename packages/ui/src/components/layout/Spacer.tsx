import React from 'react'
import { cn } from '../../utils/cn'

export interface SpacerProps extends React.HTMLAttributes<HTMLDivElement> {
  /**
   * Size of the spacer
   */
  size?: 1 | 2 | 3 | 4 | 5 | 6 | 8 | 10 | 12 | 16 | 20 | 24 | 32
  /**
   * Direction of the spacer
   */
  direction?: 'horizontal' | 'vertical' | 'both'
  /**
   * Additional CSS classes
   */
  className?: string
}

/**
 * Spacer component for adding consistent spacing between elements
 */
export const Spacer = React.forwardRef<HTMLDivElement, SpacerProps>(
  ({ size = 4, direction = 'both', className, ...props }, ref) => {
    const spacerClasses = cn(
      {
        // Horizontal spacing
        'w-1': direction === 'horizontal' && size === 1,
        'w-2': direction === 'horizontal' && size === 2,
        'w-3': direction === 'horizontal' && size === 3,
        'w-4': direction === 'horizontal' && size === 4,
        'w-5': direction === 'horizontal' && size === 5,
        'w-6': direction === 'horizontal' && size === 6,
        'w-8': direction === 'horizontal' && size === 8,
        'w-10': direction === 'horizontal' && size === 10,
        'w-12': direction === 'horizontal' && size === 12,
        'w-16': direction === 'horizontal' && size === 16,
        'w-20': direction === 'horizontal' && size === 20,
        'w-24': direction === 'horizontal' && size === 24,
        'w-32': direction === 'horizontal' && size === 32,
        
        // Vertical spacing
        'h-1': direction === 'vertical' && size === 1,
        'h-2': direction === 'vertical' && size === 2,
        'h-3': direction === 'vertical' && size === 3,
        'h-4': direction === 'vertical' && size === 4,
        'h-5': direction === 'vertical' && size === 5,
        'h-6': direction === 'vertical' && size === 6,
        'h-8': direction === 'vertical' && size === 8,
        'h-10': direction === 'vertical' && size === 10,
        'h-12': direction === 'vertical' && size === 12,
        'h-16': direction === 'vertical' && size === 16,
        'h-20': direction === 'vertical' && size === 20,
        'h-24': direction === 'vertical' && size === 24,
        'h-32': direction === 'vertical' && size === 32,
        
        // Both directions (square)
        'w-1 h-1': direction === 'both' && size === 1,
        'w-2 h-2': direction === 'both' && size === 2,
        'w-3 h-3': direction === 'both' && size === 3,
        'w-4 h-4': direction === 'both' && size === 4,
        'w-5 h-5': direction === 'both' && size === 5,
        'w-6 h-6': direction === 'both' && size === 6,
        'w-8 h-8': direction === 'both' && size === 8,
        'w-10 h-10': direction === 'both' && size === 10,
        'w-12 h-12': direction === 'both' && size === 12,
        'w-16 h-16': direction === 'both' && size === 16,
        'w-20 h-20': direction === 'both' && size === 20,
        'w-24 h-24': direction === 'both' && size === 24,
        'w-32 h-32': direction === 'both' && size === 32,
      },
      className
    )

    return (
      <div
        ref={ref}
        className={spacerClasses}
        aria-hidden="true"
        {...props}
      />
    )
  }
)

Spacer.displayName = 'Spacer'
