import React from 'react'
import { Box, BoxProps } from './Box'
import { cn } from '../../utils/cn'

export interface FlexProps extends BoxProps {
  /**
   * Flex direction
   */
  direction?: 'row' | 'row-reverse' | 'col' | 'col-reverse'
  /**
   * Flex wrap
   */
  wrap?: 'wrap' | 'wrap-reverse' | 'nowrap'
  /**
   * Justify content
   */
  justify?: 'start' | 'end' | 'center' | 'between' | 'around' | 'evenly'
  /**
   * Align items
   */
  align?: 'start' | 'end' | 'center' | 'baseline' | 'stretch'
  /**
   * Gap between items
   */
  gap?: 0 | 1 | 2 | 3 | 4 | 5 | 6 | 8 | 10 | 12 | 16 | 20 | 24
  /**
   * Whether to make this a flex container
   */
  inline?: boolean
}

/**
 * Flex is a Box with display: flex and convenient props for flexbox layouts
 */
export const Flex = React.forwardRef<HTMLDivElement, FlexProps>(
  ({ 
    direction = 'row',
    wrap,
    justify,
    align,
    gap,
    inline = false,
    className,
    ...props 
  }, ref) => {
    const flexClasses = cn(
      inline ? 'inline-flex' : 'flex',
      {
        // Direction
        'flex-row': direction === 'row',
        'flex-row-reverse': direction === 'row-reverse',
        'flex-col': direction === 'col',
        'flex-col-reverse': direction === 'col-reverse',
        
        // Wrap
        'flex-wrap': wrap === 'wrap',
        'flex-wrap-reverse': wrap === 'wrap-reverse',
        'flex-nowrap': wrap === 'nowrap',
        
        // Justify
        'justify-start': justify === 'start',
        'justify-end': justify === 'end',
        'justify-center': justify === 'center',
        'justify-between': justify === 'between',
        'justify-around': justify === 'around',
        'justify-evenly': justify === 'evenly',
        
        // Align
        'items-start': align === 'start',
        'items-end': align === 'end',
        'items-center': align === 'center',
        'items-baseline': align === 'baseline',
        'items-stretch': align === 'stretch',
        
        // Gap
        'gap-0': gap === 0,
        'gap-1': gap === 1,
        'gap-2': gap === 2,
        'gap-3': gap === 3,
        'gap-4': gap === 4,
        'gap-5': gap === 5,
        'gap-6': gap === 6,
        'gap-8': gap === 8,
        'gap-10': gap === 10,
        'gap-12': gap === 12,
        'gap-16': gap === 16,
        'gap-20': gap === 20,
        'gap-24': gap === 24,
      },
      className
    )

    return (
      <Box
        ref={ref}
        className={flexClasses}
        {...props}
      />
    )
  }
)

Flex.displayName = 'Flex'
