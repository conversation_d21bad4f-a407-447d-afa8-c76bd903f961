import React from 'react'
import { cn } from '../../utils/cn'

export interface DividerProps extends React.HTMLAttributes<HTMLDivElement> {
  /**
   * Orientation of the divider
   */
  orientation?: 'horizontal' | 'vertical'
  /**
   * Visual variant
   */
  variant?: 'solid' | 'dashed' | 'dotted'
  /**
   * Thickness of the divider
   */
  thickness?: 'thin' | 'medium' | 'thick'
  /**
   * Color variant
   */
  color?: 'default' | 'muted' | 'subtle'
  /**
   * Label to display in the center of the divider
   */
  label?: string
  /**
   * Position of the label
   */
  labelPosition?: 'left' | 'center' | 'right'
  /**
   * Additional CSS classes
   */
  className?: string
}

/**
 * Divider component for visual separation of content
 */
export const Divider = React.forwardRef<HTMLDivElement, DividerProps>(
  ({ 
    orientation = 'horizontal',
    variant = 'solid',
    thickness = 'thin',
    color = 'default',
    label,
    labelPosition = 'center',
    className,
    ...props 
  }, ref) => {
    const isHorizontal = orientation === 'horizontal'

    const dividerClasses = cn(
      'border-0',
      {
        // Orientation
        'w-full': isHorizontal,
        'h-full': !isHorizontal,
        
        // Thickness and direction
        'border-t': isHorizontal && thickness === 'thin',
        'border-t-2': isHorizontal && thickness === 'medium',
        'border-t-4': isHorizontal && thickness === 'thick',
        'border-l': !isHorizontal && thickness === 'thin',
        'border-l-2': !isHorizontal && thickness === 'medium',
        'border-l-4': !isHorizontal && thickness === 'thick',
        
        // Variant
        'border-solid': variant === 'solid',
        'border-dashed': variant === 'dashed',
        'border-dotted': variant === 'dotted',
        
        // Color
        'border-neutral-200': color === 'default',
        'border-neutral-300': color === 'muted',
        'border-neutral-100': color === 'subtle',
      },
      className
    )

    if (label && isHorizontal) {
      return (
        <div
          ref={ref}
          className={cn('relative flex items-center', className)}
          {...props}
        >
          <div
            className={cn(
              'flex-grow border-0',
              {
                'border-t': thickness === 'thin',
                'border-t-2': thickness === 'medium',
                'border-t-4': thickness === 'thick',
                'border-solid': variant === 'solid',
                'border-dashed': variant === 'dashed',
                'border-dotted': variant === 'dotted',
                'border-neutral-200': color === 'default',
                'border-neutral-300': color === 'muted',
                'border-neutral-100': color === 'subtle',
              }
            )}
          />
          <span
            className={cn(
              'px-3 text-sm text-neutral-500 bg-white',
              {
                'order-first': labelPosition === 'left',
                'order-last': labelPosition === 'right',
              }
            )}
          >
            {label}
          </span>
          <div
            className={cn(
              'flex-grow border-0',
              {
                'border-t': thickness === 'thin',
                'border-t-2': thickness === 'medium',
                'border-t-4': thickness === 'thick',
                'border-solid': variant === 'solid',
                'border-dashed': variant === 'dashed',
                'border-dotted': variant === 'dotted',
                'border-neutral-200': color === 'default',
                'border-neutral-300': color === 'muted',
                'border-neutral-100': color === 'subtle',
                'hidden': labelPosition === 'left' || labelPosition === 'right',
              }
            )}
          />
        </div>
      )
    }

    return (
      <div
        ref={ref}
        className={dividerClasses}
        role="separator"
        aria-orientation={orientation}
        {...props}
      />
    )
  }
)

Divider.displayName = 'Divider'
