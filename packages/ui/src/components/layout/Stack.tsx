import React from 'react'
import { Flex, FlexProps } from './Flex'

export interface StackProps extends Omit<FlexProps, 'direction'> {
  /**
   * The spacing between stack items
   */
  spacing?: 0 | 1 | 2 | 3 | 4 | 5 | 6 | 8 | 10 | 12 | 16 | 20 | 24
  /**
   * Whether to reverse the stack direction
   */
  reverse?: boolean
}

/**
 * Stack is a layout component that arranges its children vertically with consistent spacing
 */
export const Stack = React.forwardRef<HTMLDivElement, StackProps>(
  ({ spacing = 4, reverse = false, ...props }, ref) => {
    return (
      <Flex
        ref={ref}
        direction={reverse ? 'col-reverse' : 'col'}
        gap={spacing}
        {...props}
      />
    )
  }
)

Stack.displayName = 'Stack'

export interface HStackProps extends Omit<FlexProps, 'direction'> {
  /**
   * The spacing between stack items
   */
  spacing?: 0 | 1 | 2 | 3 | 4 | 5 | 6 | 8 | 10 | 12 | 16 | 20 | 24
  /**
   * Whether to reverse the stack direction
   */
  reverse?: boolean
}

/**
 * HStack is a layout component that arranges its children horizontally with consistent spacing
 */
export const HStack = React.forwardRef<HTMLDivElement, HStackProps>(
  ({ spacing = 4, reverse = false, ...props }, ref) => {
    return (
      <Flex
        ref={ref}
        direction={reverse ? 'row-reverse' : 'row'}
        gap={spacing}
        {...props}
      />
    )
  }
)

HStack.displayName = 'HStack'
