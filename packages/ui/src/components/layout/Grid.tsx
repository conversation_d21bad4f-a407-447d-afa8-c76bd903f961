import React from 'react'
import { Box, BoxProps } from './Box'
import { cn } from '../../utils/cn'

export interface GridProps extends BoxProps {
  /**
   * Number of columns
   */
  columns?: 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12
  /**
   * Number of rows
   */
  rows?: 1 | 2 | 3 | 4 | 5 | 6
  /**
   * Gap between grid items
   */
  gap?: 0 | 1 | 2 | 3 | 4 | 5 | 6 | 8 | 10 | 12 | 16 | 20 | 24
  /**
   * Column gap
   */
  columnGap?: 0 | 1 | 2 | 3 | 4 | 5 | 6 | 8 | 10 | 12 | 16 | 20 | 24
  /**
   * Row gap
   */
  rowGap?: 0 | 1 | 2 | 3 | 4 | 5 | 6 | 8 | 10 | 12 | 16 | 20 | 24
  /**
   * Auto-fit columns with minimum width
   */
  autoFit?: string
  /**
   * Auto-fill columns with minimum width
   */
  autoFill?: string
}

/**
 * Grid is a layout component that uses CSS Grid for two-dimensional layouts
 */
export const Grid = React.forwardRef<HTMLDivElement, GridProps>(
  ({ 
    columns,
    rows,
    gap,
    columnGap,
    rowGap,
    autoFit,
    autoFill,
    className,
    style,
    ...props 
  }, ref) => {
    const gridClasses = cn(
      'grid',
      {
        // Columns
        'grid-cols-1': columns === 1,
        'grid-cols-2': columns === 2,
        'grid-cols-3': columns === 3,
        'grid-cols-4': columns === 4,
        'grid-cols-5': columns === 5,
        'grid-cols-6': columns === 6,
        'grid-cols-7': columns === 7,
        'grid-cols-8': columns === 8,
        'grid-cols-9': columns === 9,
        'grid-cols-10': columns === 10,
        'grid-cols-11': columns === 11,
        'grid-cols-12': columns === 12,
        
        // Rows
        'grid-rows-1': rows === 1,
        'grid-rows-2': rows === 2,
        'grid-rows-3': rows === 3,
        'grid-rows-4': rows === 4,
        'grid-rows-5': rows === 5,
        'grid-rows-6': rows === 6,
        
        // Gap
        'gap-0': gap === 0,
        'gap-1': gap === 1,
        'gap-2': gap === 2,
        'gap-3': gap === 3,
        'gap-4': gap === 4,
        'gap-5': gap === 5,
        'gap-6': gap === 6,
        'gap-8': gap === 8,
        'gap-10': gap === 10,
        'gap-12': gap === 12,
        'gap-16': gap === 16,
        'gap-20': gap === 20,
        'gap-24': gap === 24,
        
        // Column gap
        'gap-x-0': columnGap === 0,
        'gap-x-1': columnGap === 1,
        'gap-x-2': columnGap === 2,
        'gap-x-3': columnGap === 3,
        'gap-x-4': columnGap === 4,
        'gap-x-5': columnGap === 5,
        'gap-x-6': columnGap === 6,
        'gap-x-8': columnGap === 8,
        'gap-x-10': columnGap === 10,
        'gap-x-12': columnGap === 12,
        'gap-x-16': columnGap === 16,
        'gap-x-20': columnGap === 20,
        'gap-x-24': columnGap === 24,
        
        // Row gap
        'gap-y-0': rowGap === 0,
        'gap-y-1': rowGap === 1,
        'gap-y-2': rowGap === 2,
        'gap-y-3': rowGap === 3,
        'gap-y-4': rowGap === 4,
        'gap-y-5': rowGap === 5,
        'gap-y-6': rowGap === 6,
        'gap-y-8': rowGap === 8,
        'gap-y-10': rowGap === 10,
        'gap-y-12': rowGap === 12,
        'gap-y-16': rowGap === 16,
        'gap-y-20': rowGap === 20,
        'gap-y-24': rowGap === 24,
      },
      className
    )

    const gridStyle: React.CSSProperties = {
      ...style,
    }

    // Handle auto-fit and auto-fill
    if (autoFit) {
      gridStyle.gridTemplateColumns = `repeat(auto-fit, minmax(${autoFit}, 1fr))`
    } else if (autoFill) {
      gridStyle.gridTemplateColumns = `repeat(auto-fill, minmax(${autoFill}, 1fr))`
    }

    return (
      <Box
        ref={ref}
        className={gridClasses}
        style={gridStyle}
        {...props}
      />
    )
  }
)

Grid.displayName = 'Grid'

export interface GridItemProps extends BoxProps {
  /**
   * Column span
   */
  colSpan?: 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12 | 'full'
  /**
   * Row span
   */
  rowSpan?: 1 | 2 | 3 | 4 | 5 | 6 | 'full'
  /**
   * Column start
   */
  colStart?: 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12 | 13 | 'auto'
  /**
   * Column end
   */
  colEnd?: 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12 | 13 | 'auto'
  /**
   * Row start
   */
  rowStart?: 1 | 2 | 3 | 4 | 5 | 6 | 7 | 'auto'
  /**
   * Row end
   */
  rowEnd?: 1 | 2 | 3 | 4 | 5 | 6 | 7 | 'auto'
}

/**
 * GridItem is a component for grid item positioning and spanning
 */
export const GridItem = React.forwardRef<HTMLDivElement, GridItemProps>(
  ({ 
    colSpan,
    rowSpan,
    colStart,
    colEnd,
    rowStart,
    rowEnd,
    className,
    ...props 
  }, ref) => {
    const gridItemClasses = cn(
      {
        // Column span
        'col-span-1': colSpan === 1,
        'col-span-2': colSpan === 2,
        'col-span-3': colSpan === 3,
        'col-span-4': colSpan === 4,
        'col-span-5': colSpan === 5,
        'col-span-6': colSpan === 6,
        'col-span-7': colSpan === 7,
        'col-span-8': colSpan === 8,
        'col-span-9': colSpan === 9,
        'col-span-10': colSpan === 10,
        'col-span-11': colSpan === 11,
        'col-span-12': colSpan === 12,
        'col-span-full': colSpan === 'full',
        
        // Row span
        'row-span-1': rowSpan === 1,
        'row-span-2': rowSpan === 2,
        'row-span-3': rowSpan === 3,
        'row-span-4': rowSpan === 4,
        'row-span-5': rowSpan === 5,
        'row-span-6': rowSpan === 6,
        'row-span-full': rowSpan === 'full',
        
        // Column start
        'col-start-1': colStart === 1,
        'col-start-2': colStart === 2,
        'col-start-3': colStart === 3,
        'col-start-4': colStart === 4,
        'col-start-5': colStart === 5,
        'col-start-6': colStart === 6,
        'col-start-7': colStart === 7,
        'col-start-8': colStart === 8,
        'col-start-9': colStart === 9,
        'col-start-10': colStart === 10,
        'col-start-11': colStart === 11,
        'col-start-12': colStart === 12,
        'col-start-13': colStart === 13,
        'col-start-auto': colStart === 'auto',
        
        // Column end
        'col-end-1': colEnd === 1,
        'col-end-2': colEnd === 2,
        'col-end-3': colEnd === 3,
        'col-end-4': colEnd === 4,
        'col-end-5': colEnd === 5,
        'col-end-6': colEnd === 6,
        'col-end-7': colEnd === 7,
        'col-end-8': colEnd === 8,
        'col-end-9': colEnd === 9,
        'col-end-10': colEnd === 10,
        'col-end-11': colEnd === 11,
        'col-end-12': colEnd === 12,
        'col-end-13': colEnd === 13,
        'col-end-auto': colEnd === 'auto',
        
        // Row start
        'row-start-1': rowStart === 1,
        'row-start-2': rowStart === 2,
        'row-start-3': rowStart === 3,
        'row-start-4': rowStart === 4,
        'row-start-5': rowStart === 5,
        'row-start-6': rowStart === 6,
        'row-start-7': rowStart === 7,
        'row-start-auto': rowStart === 'auto',
        
        // Row end
        'row-end-1': rowEnd === 1,
        'row-end-2': rowEnd === 2,
        'row-end-3': rowEnd === 3,
        'row-end-4': rowEnd === 4,
        'row-end-5': rowEnd === 5,
        'row-end-6': rowEnd === 6,
        'row-end-7': rowEnd === 7,
        'row-end-auto': rowEnd === 'auto',
      },
      className
    )

    return (
      <Box
        ref={ref}
        className={gridItemClasses}
        {...props}
      />
    )
  }
)

GridItem.displayName = 'GridItem'
