import React from 'react'
import { cn } from '../../utils/cn'

export interface BoxProps extends React.HTMLAttributes<HTMLDivElement> {
  /**
   * The HTML element to render
   */
  as?: React.ElementType
  /**
   * Additional CSS classes
   */
  className?: string
  /**
   * Children elements
   */
  children?: React.ReactNode
}

/**
 * Box is the most fundamental layout component.
 * It's a generic container with no default styling that can be used as a building block for other components.
 */
export const Box = React.forwardRef<HTMLDivElement, BoxProps>(
  ({ as: Component = 'div', className, children, ...props }, ref) => {
    return React.createElement(
      Component,
      {
        ref,
        className: cn(className),
        ...props,
      },
      children
    )
  }
)

Box.displayName = 'Box'
