import React from 'react'
import { cn } from '../../utils/cn'

export interface ProgressProps extends React.HTMLAttributes<HTMLDivElement> {
  /**
   * Current progress value (0-100)
   */
  value?: number
  /**
   * Maximum value
   */
  max?: number
  /**
   * Size variant
   */
  size?: 'sm' | 'md' | 'lg'
  /**
   * Color variant
   */
  color?: 'primary' | 'secondary' | 'success' | 'warning' | 'error'
  /**
   * Whether to show the progress value as text
   */
  showValue?: boolean
  /**
   * Custom label for the progress
   */
  label?: string
  /**
   * Whether the progress is indeterminate (loading)
   */
  indeterminate?: boolean
  /**
   * Additional CSS classes for the container
   */
  className?: string
  /**
   * Additional CSS classes for the progress bar
   */
  barClassName?: string
}

/**
 * Progress component for showing completion progress
 */
export const Progress = React.forwardRef<HTMLDivElement, ProgressProps>(
  ({ 
    value = 0,
    max = 100,
    size = 'md',
    color = 'primary',
    showValue = false,
    label,
    indeterminate = false,
    className,
    barClassName,
    ...props 
  }, ref) => {
    const percentage = indeterminate ? 0 : Math.min(Math.max((value / max) * 100, 0), 100)

    const containerClasses = cn(
      'w-full bg-neutral-200 rounded-full overflow-hidden',
      {
        'h-1': size === 'sm',
        'h-2': size === 'md',
        'h-3': size === 'lg',
      },
      className
    )

    const barClasses = cn(
      'h-full transition-all duration-300 ease-in-out rounded-full',
      {
        // Colors
        'bg-primary-500': color === 'primary',
        'bg-secondary-500': color === 'secondary',
        'bg-success-500': color === 'success',
        'bg-warning-500': color === 'warning',
        'bg-error-500': color === 'error',
        
        // Indeterminate animation
        'animate-pulse': indeterminate,
      },
      barClassName
    )

    const progressId = React.useId()

    return (
      <div className="w-full">
        {(label || showValue) && (
          <div className="flex justify-between items-center mb-2">
            {label && (
              <span className="text-sm font-medium text-neutral-700" id={progressId}>
                {label}
              </span>
            )}
            {showValue && !indeterminate && (
              <span className="text-sm text-neutral-500">
                {Math.round(percentage)}%
              </span>
            )}
          </div>
        )}
        
        <div
          ref={ref}
          className={containerClasses}
          role="progressbar"
          aria-valuenow={indeterminate ? undefined : value}
          aria-valuemin={0}
          aria-valuemax={max}
          aria-labelledby={label ? progressId : undefined}
          {...props}
        >
          <div
            className={barClasses}
            style={{
              width: indeterminate ? '100%' : `${percentage}%`,
            }}
          />
        </div>
      </div>
    )
  }
)

Progress.displayName = 'Progress'
