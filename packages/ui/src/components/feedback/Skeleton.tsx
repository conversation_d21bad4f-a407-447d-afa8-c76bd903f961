import React from 'react'
import { cn } from '../../utils/cn'

export interface SkeletonProps extends React.HTMLAttributes<HTMLDivElement> {
  /**
   * Width of the skeleton
   */
  width?: string | number
  /**
   * Height of the skeleton
   */
  height?: string | number
  /**
   * Shape of the skeleton
   */
  variant?: 'text' | 'circular' | 'rectangular' | 'rounded'
  /**
   * Whether to animate the skeleton
   */
  animate?: boolean
  /**
   * Additional CSS classes
   */
  className?: string
}

/**
 * Skeleton component for loading placeholders
 */
export const Skeleton = React.forwardRef<HTMLDivElement, SkeletonProps>(
  ({ 
    width,
    height,
    variant = 'rectangular',
    animate = true,
    className,
    style,
    ...props 
  }, ref) => {
    const skeletonClasses = cn(
      'bg-neutral-200',
      {
        // Variants
        'rounded-none': variant === 'rectangular',
        'rounded-md': variant === 'rounded',
        'rounded-full': variant === 'circular',
        'rounded h-4': variant === 'text',
        
        // Animation
        'animate-pulse': animate,
      },
      className
    )

    const skeletonStyle: React.CSSProperties = {
      width: width || (variant === 'text' ? '100%' : undefined),
      height: height || (variant === 'text' ? '1rem' : '1rem'),
      ...style,
    }

    return (
      <div
        ref={ref}
        className={skeletonClasses}
        style={skeletonStyle}
        aria-hidden="true"
        {...props}
      />
    )
  }
)

Skeleton.displayName = 'Skeleton'

export interface SkeletonTextProps {
  /**
   * Number of lines to display
   */
  lines?: number
  /**
   * Spacing between lines
   */
  spacing?: 1 | 2 | 3 | 4
  /**
   * Whether to animate the skeleton
   */
  animate?: boolean
  /**
   * Additional CSS classes
   */
  className?: string
}

/**
 * SkeletonText component for text loading placeholders
 */
export const SkeletonText: React.FC<SkeletonTextProps> = ({
  lines = 3,
  spacing = 2,
  animate = true,
  className,
}) => {
  return (
    <div
      className={cn(
        'space-y-2',
        {
          'space-y-1': spacing === 1,
          'space-y-2': spacing === 2,
          'space-y-3': spacing === 3,
          'space-y-4': spacing === 4,
        },
        className
      )}
    >
      {Array.from({ length: lines }).map((_, index) => (
        <Skeleton
          key={index}
          variant="text"
          animate={animate}
          width={index === lines - 1 ? '75%' : '100%'}
        />
      ))}
    </div>
  )
}

SkeletonText.displayName = 'SkeletonText'
