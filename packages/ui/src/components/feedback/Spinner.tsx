import React from 'react'
import { cn } from '../../utils/cn'

export interface SpinnerProps extends React.HTMLAttributes<HTMLDivElement> {
  /**
   * Size of the spinner
   */
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl'
  /**
   * Color variant
   */
  color?: 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'current'
  /**
   * Thickness of the spinner
   */
  thickness?: 'thin' | 'medium' | 'thick'
  /**
   * Speed of the animation
   */
  speed?: 'slow' | 'normal' | 'fast'
  /**
   * Label for accessibility
   */
  label?: string
  /**
   * Additional CSS classes
   */
  className?: string
}

/**
 * Spinner component for loading states
 */
export const Spinner = React.forwardRef<HTMLDivElement, SpinnerProps>(
  ({ 
    size = 'md',
    color = 'primary',
    thickness = 'medium',
    speed = 'normal',
    label = 'Loading...',
    className,
    ...props 
  }, ref) => {
    const spinnerClasses = cn(
      'animate-spin rounded-full border-solid border-current',
      {
        // Size
        'h-3 w-3': size === 'xs',
        'h-4 w-4': size === 'sm',
        'h-6 w-6': size === 'md',
        'h-8 w-8': size === 'lg',
        'h-12 w-12': size === 'xl',
        
        // Thickness
        'border': thickness === 'thin',
        'border-2': thickness === 'medium',
        'border-4': thickness === 'thick',
        
        // Color
        'text-primary-500': color === 'primary',
        'text-secondary-500': color === 'secondary',
        'text-success-500': color === 'success',
        'text-warning-500': color === 'warning',
        'text-error-500': color === 'error',
        'text-current': color === 'current',
        
        // Speed
        'animate-spin-slow': speed === 'slow',
        'animate-spin': speed === 'normal',
        'animate-spin-fast': speed === 'fast',
      },
      className
    )

    return (
      <div
        ref={ref}
        className={spinnerClasses}
        role="status"
        aria-label={label}
        {...props}
      >
        <div className="border-t-transparent border-r-transparent border-b-transparent border-l-current rounded-full h-full w-full" />
        <span className="sr-only">{label}</span>
      </div>
    )
  }
)

Spinner.displayName = 'Spinner'
