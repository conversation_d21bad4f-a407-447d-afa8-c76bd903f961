import React from 'react'
import { cn } from '../../utils/cn'

export interface LinkProps extends React.AnchorHTMLAttributes<HTMLAnchorElement> {
  /**
   * Visual variant
   */
  variant?: 'default' | 'primary' | 'secondary' | 'muted' | 'underline'
  /**
   * Size variant
   */
  size?: 'xs' | 'sm' | 'base' | 'lg' | 'xl'
  /**
   * Whether the link is external (adds external link icon and security attributes)
   */
  external?: boolean
  /**
   * Whether to show underline
   */
  underline?: boolean
  /**
   * Additional CSS classes
   */
  className?: string
  /**
   * Children elements
   */
  children?: React.ReactNode
}

/**
 * Link component for navigation and external links
 */
export const Link = React.forwardRef<HTMLAnchorElement, LinkProps>(
  ({ 
    variant = 'default',
    size = 'base',
    external = false,
    underline = false,
    className,
    children,
    href,
    target,
    rel,
    ...props 
  }, ref) => {
    const linkClasses = cn(
      'transition-colors duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2 rounded-sm',
      {
        // Size
        'text-xs': size === 'xs',
        'text-sm': size === 'sm',
        'text-base': size === 'base',
        'text-lg': size === 'lg',
        'text-xl': size === 'xl',
        
        // Variant colors
        'text-neutral-900 hover:text-neutral-700': variant === 'default',
        'text-primary-500 hover:text-primary-600': variant === 'primary',
        'text-secondary-600 hover:text-secondary-700': variant === 'secondary',
        'text-neutral-500 hover:text-neutral-600': variant === 'muted',
        'text-primary-500 hover:text-primary-600 underline underline-offset-4': variant === 'underline',
        
        // Underline
        'underline underline-offset-4': underline && variant !== 'underline',
        'hover:underline': !underline && variant !== 'underline',
      },
      className
    )

    // Handle external links
    const linkProps = {
      href,
      target: external ? '_blank' : target,
      rel: external ? 'noopener noreferrer' : rel,
      ...props,
    }

    return (
      <a
        ref={ref}
        className={linkClasses}
        {...linkProps}
      >
        {children}
        {external && (
          <svg
            className="inline-block ml-1 w-3 h-3"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            aria-hidden="true"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
            />
          </svg>
        )}
      </a>
    )
  }
)

Link.displayName = 'Link'
