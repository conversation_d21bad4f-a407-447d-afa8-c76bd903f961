import React from 'react'
import { cva, type VariantProps } from 'class-variance-authority'
import { cn } from '../../utils/cn'

const typographyVariants = cva('', {
  variants: {
    variant: {
      // Display variants
      h1: 'scroll-m-20 text-4xl font-extrabold tracking-tight lg:text-5xl',
      h2: 'scroll-m-20 border-b pb-2 text-3xl font-semibold tracking-tight first:mt-0',
      h3: 'scroll-m-20 text-2xl font-semibold tracking-tight',
      h4: 'scroll-m-20 text-xl font-semibold tracking-tight',
      h5: 'scroll-m-20 text-lg font-semibold tracking-tight',
      h6: 'scroll-m-20 text-base font-semibold tracking-tight',
      
      // Body variants
      body1: 'text-base leading-7',
      body2: 'text-sm leading-6',
      
      // Subtitle variants
      subtitle1: 'text-base font-medium leading-6',
      subtitle2: 'text-sm font-medium leading-5',
      
      // Caption and overline
      caption: 'text-xs leading-4 text-neutral-600',
      overline: 'text-xs font-medium uppercase tracking-wider leading-4 text-neutral-600',
      
      // Button text
      button: 'text-sm font-medium leading-5',
      
      // Inherit from parent
      inherit: '',
    },
    color: {
      primary: 'text-primary-500',
      secondary: 'text-secondary-600',
      success: 'text-success-500',
      warning: 'text-warning-500',
      error: 'text-error-500',
      info: 'text-primary-500',
      textPrimary: 'text-neutral-900',
      textSecondary: 'text-neutral-600',
      textDisabled: 'text-neutral-400',
      inherit: '',
    },
    align: {
      left: 'text-left',
      center: 'text-center',
      right: 'text-right',
      justify: 'text-justify',
      inherit: '',
    },
    display: {
      initial: '',
      block: 'block',
      inline: 'inline',
      'inline-block': 'inline-block',
    },
  },
  defaultVariants: {
    variant: 'body1',
    color: 'textPrimary',
    align: 'inherit',
    display: 'initial',
  },
})

// Mapping of variants to default HTML elements
const variantMapping = {
  h1: 'h1',
  h2: 'h2',
  h3: 'h3',
  h4: 'h4',
  h5: 'h5',
  h6: 'h6',
  subtitle1: 'h6',
  subtitle2: 'h6',
  body1: 'p',
  body2: 'p',
  caption: 'span',
  button: 'span',
  overline: 'span',
  inherit: 'p',
} as const

export interface TypographyProps
  extends Omit<React.HTMLAttributes<HTMLElement>, 'color'>,
    VariantProps<typeof typographyVariants> {
  /**
   * The component used for the root node.
   * Either a string to use a HTML element or a component.
   */
  component?: React.ElementType
  /**
   * The content of the component.
   */
  children?: React.ReactNode
  /**
   * Additional CSS classes
   */
  className?: string
  /**
   * If true, the text will have a bottom margin.
   */
  gutterBottom?: boolean
  /**
   * If true, the text will not wrap, but instead will truncate with a text overflow ellipsis.
   */
  noWrap?: boolean
  /**
   * If true, the element will be a paragraph element.
   */
  paragraph?: boolean
}

/**
 * Typography component for consistent text styling across the application.
 * Similar to Material UI's Typography component with variant and component props.
 */
export const Typography = React.forwardRef<HTMLElement, TypographyProps>(
  (
    {
      variant = 'body1',
      color = 'textPrimary',
      align = 'inherit',
      display = 'initial',
      component,
      className,
      children,
      gutterBottom = false,
      noWrap = false,
      paragraph = false,
      ...props
    },
    ref
  ) => {
    // Determine the component to render
    const Component = component || (paragraph ? 'p' : variantMapping[variant!]) || 'span'

    const typographyClasses = cn(
      typographyVariants({ variant, color, align, display }),
      {
        'mb-4': gutterBottom,
        'truncate': noWrap,
      },
      className
    )

    return React.createElement(
      Component,
      {
        ref,
        className: typographyClasses,
        ...props,
      },
      children
    )
  }
)

Typography.displayName = 'Typography'

// Export the variant types for external use
export type TypographyVariant = NonNullable<VariantProps<typeof typographyVariants>['variant']>
export type TypographyColor = NonNullable<VariantProps<typeof typographyVariants>['color']>
export type TypographyAlign = NonNullable<VariantProps<typeof typographyVariants>['align']>
export type TypographyDisplay = NonNullable<VariantProps<typeof typographyVariants>['display']>
