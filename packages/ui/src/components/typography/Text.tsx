import React from 'react'
import { cn } from '../../utils/cn'
import { textVariants, type TextVariants } from '../../utils/variants'

export interface TextProps extends Omit<React.HTMLAttributes<HTMLElement>, 'color'>, TextVariants {
  /**
   * The HTML element to render
   */
  as?: 'p' | 'span' | 'div' | 'label' | 'strong' | 'em' | 'small' | 'mark' | 'del' | 'ins' | 'sub' | 'sup'
  /**
   * Additional CSS classes
   */
  className?: string
  /**
   * Children elements
   */
  children?: React.ReactNode
  /**
   * Whether the text should be truncated with ellipsis
   */
  truncate?: boolean
  /**
   * Number of lines to clamp (requires line-clamp utility)
   */
  lineClamp?: 1 | 2 | 3 | 4 | 5 | 6
}

/**
 * Text component for rendering text with consistent typography styles
 */
export const Text = React.forwardRef<HTMLElement, TextProps>(
  ({ 
    as: Component = 'p',
    variant,
    size,
    weight,
    color,
    truncate = false,
    lineClamp,
    className,
    children,
    ...props 
  }, ref) => {
    const textClasses = cn(
      textVariants({ variant, size, weight, color }),
      {
        'truncate': truncate,
        'line-clamp-1': lineClamp === 1,
        'line-clamp-2': lineClamp === 2,
        'line-clamp-3': lineClamp === 3,
        'line-clamp-4': lineClamp === 4,
        'line-clamp-5': lineClamp === 5,
        'line-clamp-6': lineClamp === 6,
      },
      className
    )

    return (
      <Component
        ref={ref as any}
        className={textClasses}
        {...props}
      >
        {children}
      </Component>
    )
  }
)

Text.displayName = 'Text'
