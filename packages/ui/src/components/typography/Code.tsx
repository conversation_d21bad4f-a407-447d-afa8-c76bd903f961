import React from 'react'
import { cn } from '../../utils/cn'

export interface CodeProps extends React.HTMLAttributes<HTMLElement> {
  /**
   * The HTML element to render
   */
  as?: 'code' | 'pre'
  /**
   * Size variant
   */
  size?: 'xs' | 'sm' | 'base' | 'lg'
  /**
   * Color variant
   */
  variant?: 'default' | 'primary' | 'success' | 'warning' | 'error'
  /**
   * Whether to display as a block element
   */
  block?: boolean
  /**
   * Additional CSS classes
   */
  className?: string
  /**
   * Children elements
   */
  children?: React.ReactNode
}

/**
 * Code component for displaying inline code or code blocks
 */
export const Code = React.forwardRef<HTMLElement, CodeProps>(
  ({ 
    as: Component = 'code',
    size = 'sm',
    variant = 'default',
    block = false,
    className,
    children,
    ...props 
  }, ref) => {
    const codeClasses = cn(
      'font-mono rounded',
      {
        // Size
        'text-xs': size === 'xs',
        'text-sm': size === 'sm',
        'text-base': size === 'base',
        'text-lg': size === 'lg',
        
        // Variant colors
        'bg-neutral-100 text-neutral-900': variant === 'default',
        'bg-primary-100 text-primary-900': variant === 'primary',
        'bg-success-100 text-success-900': variant === 'success',
        'bg-warning-100 text-warning-900': variant === 'warning',
        'bg-error-100 text-error-900': variant === 'error',
        
        // Block vs inline
        'block p-4 overflow-x-auto': block,
        'inline px-1.5 py-0.5': !block,
      },
      className
    )

    const ElementComponent = block ? 'pre' : Component

    return (
      <ElementComponent
        ref={ref as any}
        className={codeClasses}
        {...props}
      >
        {block ? <code>{children}</code> : children}
      </ElementComponent>
    )
  }
)

Code.displayName = 'Code'
