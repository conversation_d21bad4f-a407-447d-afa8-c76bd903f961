import React from 'react'
import { cn } from '../../utils/cn'
import { textVariants } from '../../utils/variants'

export interface HeadingProps extends React.HTMLAttributes<HTMLHeadingElement> {
  /**
   * The heading level (h1-h6)
   */
  as?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6'
  /**
   * Visual size variant (can be different from semantic level)
   */
  size?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6'
  /**
   * Text color
   */
  color?: 'default' | 'muted' | 'subtle' | 'primary' | 'success' | 'warning' | 'error'
  /**
   * Font weight
   */
  weight?: 'normal' | 'medium' | 'semibold' | 'bold'
  /**
   * Additional CSS classes
   */
  className?: string
  /**
   * Children elements
   */
  children?: React.ReactNode
  /**
   * Whether the heading should be truncated with ellipsis
   */
  truncate?: boolean
}

/**
 * Heading component for rendering semantic headings with consistent typography
 */
export const Heading = React.forwardRef<HTMLHeadingElement, HeadingProps>(
  ({ 
    as: Component = 'h2',
    size,
    color = 'default',
    weight,
    truncate = false,
    className,
    children,
    ...props 
  }, ref) => {
    // Default size based on semantic level if not provided
    const defaultSize = size || Component
    
    // Default weight based on heading level
    const defaultWeight = weight || (
      defaultSize === 'h1' ? 'bold' :
      defaultSize === 'h2' ? 'semibold' :
      defaultSize === 'h3' ? 'semibold' :
      defaultSize === 'h4' ? 'semibold' :
      defaultSize === 'h5' ? 'semibold' :
      'medium'
    )

    const headingClasses = cn(
      textVariants({ 
        variant: defaultSize as any, 
        color, 
        weight: defaultWeight 
      }),
      {
        'truncate': truncate,
      },
      className
    )

    return (
      <Component
        ref={ref}
        className={headingClasses}
        {...props}
      >
        {children}
      </Component>
    )
  }
)

Heading.displayName = 'Heading'
