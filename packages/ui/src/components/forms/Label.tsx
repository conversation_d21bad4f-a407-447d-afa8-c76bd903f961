import React from 'react'
import { cn } from '../../utils/cn'

export interface LabelProps extends React.LabelHTMLAttributes<HTMLLabelElement> {
  /**
   * Size variant
   */
  size?: 'sm' | 'md' | 'lg'
  /**
   * Whether the field is required
   */
  required?: boolean
  /**
   * Whether the field is disabled
   */
  disabled?: boolean
  /**
   * Additional CSS classes
   */
  className?: string
  /**
   * Children elements
   */
  children?: React.ReactNode
}

/**
 * Label component for form field labels
 */
export const Label = React.forwardRef<HTMLLabelElement, LabelProps>(
  ({ 
    size = 'md',
    required = false,
    disabled = false,
    className,
    children,
    ...props 
  }, ref) => {
    const labelClasses = cn(
      'block font-medium text-neutral-900',
      {
        // Size
        'text-sm': size === 'sm',
        'text-base': size === 'md',
        'text-lg': size === 'lg',
        
        // Disabled
        'opacity-50 cursor-not-allowed': disabled,
        'cursor-pointer': !disabled,
      },
      className
    )

    return (
      <label
        ref={ref}
        className={labelClasses}
        {...props}
      >
        {children}
        {required && (
          <span className="text-error-500 ml-1" aria-label="required">
            *
          </span>
        )}
      </label>
    )
  }
)

Label.displayName = 'Label'
