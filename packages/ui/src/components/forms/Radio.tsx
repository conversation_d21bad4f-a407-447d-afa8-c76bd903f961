import React from 'react'
import { cn } from '../../utils/cn'

export interface RadioProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'size'> {
  /**
   * Size variant
   */
  size?: 'sm' | 'md' | 'lg'
  /**
   * Label text
   */
  label?: string
  /**
   * Description text
   */
  description?: string
  /**
   * Additional CSS classes for the container
   */
  className?: string
  /**
   * Additional CSS classes for the input
   */
  inputClassName?: string
  /**
   * Additional CSS classes for the label
   */
  labelClassName?: string
}

/**
 * Radio component for single selection from a group
 */
export const Radio = React.forwardRef<HTMLInputElement, RadioProps>(
  ({ 
    size = 'md',
    label,
    description,
    className,
    inputClassName,
    labelClassName,
    disabled,
    id,
    ...props 
  }, ref) => {
    const radioClasses = cn(
      'border-neutral-300 text-primary-600 focus:ring-primary-500 focus:ring-2 focus:ring-offset-2',
      {
        // Size
        'h-3 w-3': size === 'sm',
        'h-4 w-4': size === 'md',
        'h-5 w-5': size === 'lg',
        
        // Disabled
        'opacity-50 cursor-not-allowed': disabled,
      },
      inputClassName
    )

    const labelClasses = cn(
      'font-medium text-neutral-900',
      {
        'text-sm': size === 'sm',
        'text-base': size === 'md',
        'text-lg': size === 'lg',
        
        // Disabled
        'opacity-50 cursor-not-allowed': disabled,
        'cursor-pointer': !disabled,
      },
      labelClassName
    )

    const generatedId = id || `radio-${Math.random().toString(36).substr(2, 9)}`

    if (!label && !description) {
      return (
        <input
          ref={ref}
          type="radio"
          className={radioClasses}
          disabled={disabled}
          id={generatedId}
          {...props}
        />
      )
    }

    return (
      <div className={cn('flex items-start', className)}>
        <div className="flex items-center h-5">
          <input
            ref={ref}
            type="radio"
            className={radioClasses}
            disabled={disabled}
            id={generatedId}
            {...props}
          />
        </div>
        {(label || description) && (
          <div className="ml-3 text-sm">
            {label && (
              <label htmlFor={generatedId} className={labelClasses}>
                {label}
              </label>
            )}
            {description && (
              <p className="text-neutral-500">{description}</p>
            )}
          </div>
        )}
      </div>
    )
  }
)

Radio.displayName = 'Radio'

export interface RadioGroupProps {
  /**
   * The name attribute for all radio inputs in the group
   */
  name: string
  /**
   * The currently selected value
   */
  value?: string
  /**
   * Default selected value
   */
  defaultValue?: string
  /**
   * Callback when selection changes
   */
  onChange?: (value: string) => void
  /**
   * Whether the entire group is disabled
   */
  disabled?: boolean
  /**
   * Size for all radio inputs in the group
   */
  size?: 'sm' | 'md' | 'lg'
  /**
   * Layout direction
   */
  direction?: 'row' | 'column'
  /**
   * Gap between radio items
   */
  gap?: 2 | 3 | 4 | 6 | 8
  /**
   * Additional CSS classes
   */
  className?: string
  /**
   * Children (Radio components)
   */
  children: React.ReactNode
}

/**
 * RadioGroup component for managing a group of radio buttons
 */
export const RadioGroup: React.FC<RadioGroupProps> = ({
  name,
  value,
  defaultValue,
  onChange,
  disabled = false,
  size = 'md',
  direction = 'column',
  gap = 4,
  className,
  children,
}) => {
  const [selectedValue, setSelectedValue] = React.useState(defaultValue || '')

  const currentValue = value !== undefined ? value : selectedValue

  const handleChange = (newValue: string) => {
    if (value === undefined) {
      setSelectedValue(newValue)
    }
    onChange?.(newValue)
  }

  const groupClasses = cn(
    'flex',
    {
      'flex-col': direction === 'column',
      'flex-row flex-wrap': direction === 'row',
      'gap-2': gap === 2,
      'gap-3': gap === 3,
      'gap-4': gap === 4,
      'gap-6': gap === 6,
      'gap-8': gap === 8,
    },
    className
  )

  return (
    <div className={groupClasses} role="radiogroup">
      {React.Children.map(children, (child) => {
        if (React.isValidElement(child) && child.type === Radio) {
          const childProps = child.props as RadioProps
          return React.cloneElement(child as React.ReactElement<RadioProps>, {
            name,
            size: childProps.size || size,
            disabled: childProps.disabled || disabled,
            checked: childProps.value === currentValue,
            onChange: (e: React.ChangeEvent<HTMLInputElement>) => {
              if (e.target.checked) {
                handleChange(childProps.value as string)
              }
              childProps.onChange?.(e)
            },
          })
        }
        return child
      })}
    </div>
  )
}
