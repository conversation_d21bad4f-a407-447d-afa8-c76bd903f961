import React from 'react'
import { cn } from '../../utils/cn'

export interface CheckboxProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'size'> {
  /**
   * Size variant
   */
  size?: 'sm' | 'md' | 'lg'
  /**
   * Label text
   */
  label?: string
  /**
   * Description text
   */
  description?: string
  /**
   * Whether the checkbox is in an indeterminate state
   */
  indeterminate?: boolean
  /**
   * Additional CSS classes for the container
   */
  className?: string
  /**
   * Additional CSS classes for the input
   */
  inputClassName?: string
  /**
   * Additional CSS classes for the label
   */
  labelClassName?: string
}

/**
 * Checkbox component for boolean input
 */
export const Checkbox = React.forwardRef<HTMLInputElement, CheckboxProps>(
  ({ 
    size = 'md',
    label,
    description,
    indeterminate = false,
    className,
    inputClassName,
    labelClassName,
    disabled,
    id,
    ...props 
  }, ref) => {
    const checkboxRef = React.useRef<HTMLInputElement>(null)
    
    // Handle indeterminate state
    React.useEffect(() => {
      const checkbox = checkboxRef.current
      if (checkbox) {
        checkbox.indeterminate = indeterminate
      }
    }, [indeterminate])

    // Merge refs
    React.useImperativeHandle(ref, () => checkboxRef.current!)

    const checkboxClasses = cn(
      'rounded border-neutral-300 text-primary-600 focus:ring-primary-500 focus:ring-2 focus:ring-offset-2',
      {
        // Size
        'h-3 w-3': size === 'sm',
        'h-4 w-4': size === 'md',
        'h-5 w-5': size === 'lg',
        
        // Disabled
        'opacity-50 cursor-not-allowed': disabled,
      },
      inputClassName
    )

    const labelClasses = cn(
      'font-medium text-neutral-900',
      {
        'text-sm': size === 'sm',
        'text-base': size === 'md',
        'text-lg': size === 'lg',
        
        // Disabled
        'opacity-50 cursor-not-allowed': disabled,
        'cursor-pointer': !disabled,
      },
      labelClassName
    )

    const generatedId = id || `checkbox-${Math.random().toString(36).substr(2, 9)}`

    if (!label && !description) {
      return (
        <input
          ref={checkboxRef}
          type="checkbox"
          className={checkboxClasses}
          disabled={disabled}
          id={generatedId}
          {...props}
        />
      )
    }

    return (
      <div className={cn('flex items-start', className)}>
        <div className="flex items-center h-5">
          <input
            ref={checkboxRef}
            type="checkbox"
            className={checkboxClasses}
            disabled={disabled}
            id={generatedId}
            {...props}
          />
        </div>
        {(label || description) && (
          <div className="ml-3 text-sm">
            {label && (
              <label htmlFor={generatedId} className={labelClasses}>
                {label}
              </label>
            )}
            {description && (
              <p className="text-neutral-500">{description}</p>
            )}
          </div>
        )}
      </div>
    )
  }
)

Checkbox.displayName = 'Checkbox'
