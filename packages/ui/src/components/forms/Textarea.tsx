import React from 'react'
import { cn } from '../../utils/cn'

export interface TextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  /**
   * Size variant
   */
  size?: 'sm' | 'md' | 'lg'
  /**
   * Visual variant
   */
  variant?: 'default' | 'error' | 'success'
  /**
   * Whether the textarea has an error state
   */
  error?: boolean
  /**
   * Whether to resize automatically based on content
   */
  autoResize?: boolean
  /**
   * Additional CSS classes
   */
  className?: string
}

/**
 * Textarea component for multi-line text input
 */
export const Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(
  ({ 
    size = 'md',
    variant = 'default',
    error = false,
    autoResize = false,
    className,
    disabled,
    ...props 
  }, ref) => {
    const textareaVariant = error ? 'error' : variant

    const textareaClasses = cn(
      'flex min-h-[80px] w-full rounded-md border border-neutral-200 bg-white px-3 py-2 text-sm ring-offset-white placeholder:text-neutral-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',
      {
        // Size
        'min-h-[60px] px-2 py-1 text-xs': size === 'sm',
        'min-h-[80px] px-3 py-2 text-sm': size === 'md',
        'min-h-[100px] px-4 py-3 text-base': size === 'lg',
        
        // Variant
        'border-error-500 focus-visible:ring-error-500': textareaVariant === 'error',
        'border-success-500 focus-visible:ring-success-500': textareaVariant === 'success',
        
        // Auto resize
        'resize-none': autoResize,
        'resize-y': !autoResize,
      },
      className
    )

    return (
      <textarea
        ref={ref}
        className={textareaClasses}
        disabled={disabled}
        {...props}
      />
    )
  }
)

Textarea.displayName = 'Textarea'
