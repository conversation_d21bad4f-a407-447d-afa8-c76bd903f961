import React from 'react'
import { cn } from '../../utils/cn'
import { Label } from './Label'

export interface FormFieldProps {
  /**
   * The form field label
   */
  label?: string
  /**
   * Whether the field is required
   */
  required?: boolean
  /**
   * Error message to display
   */
  error?: string
  /**
   * Helper text to display below the field
   */
  helperText?: string
  /**
   * Size variant for the label and spacing
   */
  size?: 'sm' | 'md' | 'lg'
  /**
   * Whether the field is disabled
   */
  disabled?: boolean
  /**
   * Additional CSS classes for the container
   */
  className?: string
  /**
   * Additional CSS classes for the label
   */
  labelClassName?: string
  /**
   * The form control element (Input, Textarea, etc.)
   */
  children: React.ReactNode
}

/**
 * FormField component that wraps form controls with label, error, and helper text
 */
export const FormField: React.FC<FormFieldProps> = ({
  label,
  required = false,
  error,
  helperText,
  size = 'md',
  disabled = false,
  className,
  labelClassName,
  children,
}) => {
  const fieldId = React.useId()

  const containerClasses = cn(
    'space-y-2',
    {
      'space-y-1': size === 'sm',
      'space-y-2': size === 'md',
      'space-y-3': size === 'lg',
    },
    className
  )

  // Clone the child element to add the id and error state
  const childWithProps = React.isValidElement(children)
    ? React.cloneElement(children as React.ReactElement<any>, {
        id: fieldId,
        error: !!error,
        disabled,
        'aria-describedby': error ? `${fieldId}-error` : helperText ? `${fieldId}-helper` : undefined,
        'aria-invalid': !!error,
      })
    : children

  return (
    <div className={containerClasses}>
      {label && (
        <Label
          htmlFor={fieldId}
          required={required}
          disabled={disabled}
          size={size}
          className={labelClassName}
        >
          {label}
        </Label>
      )}
      
      {childWithProps}
      
      {error && (
        <p
          id={`${fieldId}-error`}
          className={cn(
            'text-error-500',
            {
              'text-xs': size === 'sm',
              'text-sm': size === 'md',
              'text-base': size === 'lg',
            }
          )}
          role="alert"
        >
          {error}
        </p>
      )}
      
      {!error && helperText && (
        <p
          id={`${fieldId}-helper`}
          className={cn(
            'text-neutral-500',
            {
              'text-xs': size === 'sm',
              'text-sm': size === 'md',
              'text-base': size === 'lg',
            }
          )}
        >
          {helperText}
        </p>
      )}
    </div>
  )
}

FormField.displayName = 'FormField'
