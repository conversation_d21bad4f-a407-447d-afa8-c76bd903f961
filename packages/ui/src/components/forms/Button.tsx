import React from 'react'
import { cn } from '../../utils/cn'
import { buttonVariants, type ButtonVariants } from '../../utils/variants'

export interface ButtonProps 
  extends React.ButtonHTMLAttributes<HTMLButtonElement>, 
         ButtonVariants {
  /**
   * The HTML element to render (button or a for link buttons)
   */
  as?: 'button' | 'a'
  /**
   * Whether the button is in a loading state
   */
  loading?: boolean
  /**
   * Icon to display before the button text
   */
  leftIcon?: React.ReactNode
  /**
   * Icon to display after the button text
   */
  rightIcon?: React.ReactNode
  /**
   * Additional CSS classes
   */
  className?: string
  /**
   * Children elements
   */
  children?: React.ReactNode
}

/**
 * Button component for user interactions
 */
export const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ 
    as: Component = 'button',
    variant,
    size,
    loading = false,
    leftIcon,
    rightIcon,
    disabled,
    className,
    children,
    ...props 
  }, ref) => {
    const buttonClasses = cn(
      buttonVariants({ variant, size }),
      {
        'cursor-not-allowed opacity-50': disabled || loading,
      },
      className
    )

    const content = (
      <>
        {loading && (
          <svg
            className="animate-spin -ml-1 mr-2 h-4 w-4"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
            />
            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            />
          </svg>
        )}
        {!loading && leftIcon && (
          <span className="mr-2">{leftIcon}</span>
        )}
        {children}
        {!loading && rightIcon && (
          <span className="ml-2">{rightIcon}</span>
        )}
      </>
    )

    if (Component === 'a') {
      return (
        <a
          ref={ref as any}
          className={buttonClasses}
          {...(props as any)}
        >
          {content}
        </a>
      )
    }

    return (
      <button
        ref={ref}
        className={buttonClasses}
        disabled={disabled || loading}
        {...props}
      >
        {content}
      </button>
    )
  }
)

Button.displayName = 'Button'
