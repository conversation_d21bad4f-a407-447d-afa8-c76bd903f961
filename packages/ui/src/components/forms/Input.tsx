import React from 'react'
import { cn } from '../../utils/cn'
import { inputVariants, type InputVariants } from '../../utils/variants'

export interface InputProps
  extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'size'>,
         InputVariants {
  /**
   * Icon to display before the input
   */
  leftIcon?: React.ReactNode
  /**
   * Icon to display after the input
   */
  rightIcon?: React.ReactNode
  /**
   * Additional element to display after the input (e.g., button)
   */
  rightElement?: React.ReactNode
  /**
   * Whether the input has an error state
   */
  error?: boolean
  /**
   * Additional CSS classes
   */
  className?: string
}

/**
 * Input component for text input fields
 */
export const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ 
    size,
    variant,
    leftIcon,
    rightIcon,
    rightElement,
    error = false,
    className,
    disabled,
    ...props 
  }, ref) => {
    const inputVariant = error ? 'error' : variant

    if (leftIcon || rightIcon || rightElement) {
      return (
        <div className="relative">
          {leftIcon && (
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <span className="text-neutral-500 text-sm">{leftIcon}</span>
            </div>
          )}
          <input
            ref={ref}
            className={cn(
              inputVariants({ size, variant: inputVariant }),
              {
                'pl-10': leftIcon,
                'pr-10': rightIcon && !rightElement,
                'pr-12': rightElement,
              },
              className
            )}
            disabled={disabled}
            {...props}
          />
          {rightIcon && !rightElement && (
            <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
              <span className="text-neutral-500 text-sm">{rightIcon}</span>
            </div>
          )}
          {rightElement && (
            <div className="absolute inset-y-0 right-0 flex items-center">
              {rightElement}
            </div>
          )}
        </div>
      )
    }

    return (
      <input
        ref={ref}
        className={cn(
          inputVariants({ size, variant: inputVariant }),
          className
        )}
        disabled={disabled}
        {...props}
      />
    )
  }
)

Input.displayName = 'Input'
