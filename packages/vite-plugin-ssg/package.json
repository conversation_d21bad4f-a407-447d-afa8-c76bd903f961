{"name": "@augment/vite-plugin-ssg", "version": "0.1.0", "description": "A Vite plugin for Static Site Generation with React Server Components and client-side hydration", "type": "module", "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./client": {"types": "./dist/client.d.ts", "import": "./dist/client.js"}}, "files": ["dist", "client.d.ts"], "scripts": {"dev": "vite build --watch", "build": "npm run build:types && vite build", "build:types": "tsc -p tsconfig.build.json", "clean": "rm -rf dist", "typecheck": "tsc --noEmit", "lint": "echo \"No linting configured yet\"", "test": "echo \"No tests configured yet\""}, "keywords": ["vite", "plugin", "ssg", "static-site-generation", "react", "server-components", "hydration", "typescript"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "peerDependencies": {"react": ">=18.0.0", "react-dom": ">=18.0.0", "vite": ">=5.0.0"}, "dependencies": {"@types/node": "^24.0.4", "fast-glob": "^3.3.2", "magic-string": "^0.30.17"}, "devDependencies": {"@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "react": "^19.1.0", "react-dom": "^19.1.0", "typescript": "^5.8.3", "vite": "^5.4.19"}}