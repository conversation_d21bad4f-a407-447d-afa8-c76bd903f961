# @augment/vite-plugin-ssg Documentation

A comprehensive Vite plugin for Static Site Generation (SSG) with React Server Components and selective client-side hydration.

## Table of Contents

- [Installation](#installation)
- [Quick Start](#quick-start)
- [Configuration](#configuration)
- [React Server Components](#react-server-components)
- [Client-side Hydration](#client-side-hydration)
- [API Reference](#api-reference)
- [Examples](#examples)

## Installation

```bash
npm install @augment/vite-plugin-ssg
# or
yarn add @augment/vite-plugin-ssg
# or
pnpm add @augment/vite-plugin-ssg
```

## Quick Start

### 1. Configure Vite

```typescript
// vite.config.ts
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import { ssg } from '@augment/vite-plugin-ssg'

export default defineConfig({
  plugins: [
    react(),
    ssg({
      entry: 'src/App.tsx',
      outDir: 'dist',
      hydration: true
    })
  ]
})
```

### 2. Create Your App

```typescript
// src/App.tsx
import React, { useState } from 'react'
import { Hydrate } from '@augment/vite-plugin-ssg/client'

export default function App() {
  return (
    <div>
      <h1>Welcome to SSG</h1>
      <p>This content is server-rendered!</p>

      <Hydrate>
        <Counter />
      </Hydrate>
    </div>
  )
}

function Counter() {
  const [count, setCount] = useState(0)

  return (
    <div>
      <p>Count: {count}</p>
      <button onClick={() => setCount(c => c + 1)}>
        Increment
      </button>
    </div>
  )
}
```

### 3. Build Your Site

```bash
npm run build
```

## Configuration

The plugin accepts a configuration object with the following options:

```typescript
interface SSGConfig {
  /** Entry component file (default: 'src/App.tsx') */
  entry?: string

  /** Output directory for generated files (default: 'dist') */
  outDir?: string

  /** Custom HTML template */
  template?: string

  /** Whether to enable client-side hydration (default: true) */
  hydration?: boolean

  /** Base URL for the site (default: '/') */
  base?: string

  /** Custom render function for the app */
  render?: (component: React.ComponentType) => Promise<string> | string
}
```

### Example Configuration

```typescript
// vite.config.ts
export default defineConfig({
  plugins: [
    react(),
    ssg({
      entry: 'src/App.tsx',
      outDir: 'dist',
      base: '/my-app/',
      hydration: true,
      template: `
        <!DOCTYPE html>
        <html>
          <head>
            <title>My SSG App</title>
            {{base}}
            {{assets}}
          </head>
          <body>
            <div id="root">{{html}}</div>
          </body>
        </html>
      `
    })
  ]
})
```

## Single Page Application

The plugin generates a single `index.html` file from your main App component. This approach is perfect for:

- Single-page applications (SPAs)
- Landing pages
- Documentation sites
- Portfolio websites
- Simple marketing sites

Your app component serves as the entry point and can contain both static and interactive content.

## React Server Components

The plugin supports React Server Components out of the box. Server components run only on the server during build time:

```typescript
// src/App.tsx
import { BlogList } from './components/BlogList.server'
import { Hydrate } from '@augment/vite-plugin-ssg/client'
import { SearchBox } from './components/SearchBox'

export default async function App() {
  // This runs on the server during build
  const posts = await fetchBlogPosts()

  return (
    <div>
      <h1>My Blog</h1>

      {/* Server component - no JavaScript sent to client */}
      <BlogList posts={posts} />

      {/* Client component - hydrated on the client */}
      <Hydrate>
        <SearchBox />
      </Hydrate>
    </div>
  )
}
```

## Client-side Hydration

Use the `Hydrate` component to mark parts of your page for client-side hydration:

### Basic Usage

```typescript
import { Hydrate } from '@augment/vite-plugin-ssg/client'

function MyPage() {
  return (
    <div>
      <h1>Static Content</h1>
      
      <Hydrate>
        <InteractiveComponent />
      </Hydrate>
    </div>
  )
}
```

### With Fallback

```typescript
<Hydrate fallback={<div>Loading...</div>}>
  <ExpensiveComponent />
</Hydrate>
```

### Using the Hook

```typescript
import { useHydration } from '@augment/vite-plugin-ssg/client'

function MyComponent() {
  const { isHydrated, hydrate } = useHydration('my-component')
  
  if (!isHydrated) {
    return <div>Static content</div>
  }
  
  return <div>Interactive content</div>
}
```

### Higher-Order Component

```typescript
import { withHydration } from '@augment/vite-plugin-ssg/client'

const HydratedComponent = withHydration(MyComponent, {
  fallback: <div>Loading...</div>
})
```

## API Reference

### Plugin API

#### `ssg(config?: SSGConfig)`

Creates the SSG plugin with optional configuration.

### Client API

#### `hydrate()`

Initialize hydration for the application.

#### `Hydrate`

Component wrapper for selective hydration.

#### `withHydration(Component, options?)`

Higher-order component for automatic hydration.

#### `useHydration(id?)`

Hook for hydration state management.

## Examples

### Blog Application

```typescript
// src/App.tsx
export default function App() {
  const posts = [
    { id: 1, title: 'Hello World', content: 'Welcome to my blog!' },
    { id: 2, title: 'SSG is Great', content: 'Static generation rocks!' }
  ]

  return (
    <div>
      <header>
        <h1>My Blog</h1>
      </header>

      <main>
        {posts.map(post => (
          <article key={post.id}>
            <h2>{post.title}</h2>
            <p>{post.content}</p>

            <Hydrate>
              <LikeButton postId={post.id} />
            </Hydrate>
          </article>
        ))}
      </main>

      <Hydrate>
        <CommentSection />
      </Hydrate>
    </div>
  )
}
```

### Portfolio Website

```typescript
// src/App.tsx
export default function App() {
  const projects = await fetchProjects() // Server-side data fetching

  return (
    <div>
      <header>
        <h1>John Doe</h1>
        <p>Full Stack Developer</p>
      </header>

      <section>
        <h2>Projects</h2>
        {projects.map(project => (
          <div key={project.id}>
            <h3>{project.name}</h3>
            <p>{project.description}</p>

            <Hydrate>
              <ProjectGallery images={project.images} />
            </Hydrate>
          </div>
        ))}
      </section>

      <Hydrate>
        <ContactForm />
      </Hydrate>
    </div>
  )
}
```

For more examples and advanced usage, see the [examples directory](./examples/).
