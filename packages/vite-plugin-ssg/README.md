# @augment/vite-plugin-ssg

A Vite plugin for Static Site Generation (SSG) with React Server Components and client-side hydration.

## Features

- 🚀 **Static Site Generation**: Pre-render React applications at build time
- ⚛️ **React Server Components**: Full support for RSC during SSG
- 💧 **Selective Hydration**: Hydrate only interactive components on the client
- 🔧 **Vite Integration**: Seamless integration with Vite's build system
- 📄 **Single Page Focus**: Generates a single optimized index.html
- ⚡ **Fast Builds**: Optimized build process
- 🎯 **TypeScript**: Full TypeScript support with comprehensive types

## Installation

```bash
npm install @augment/vite-plugin-ssg
```

## Quick Start

### 1. Configure Vite

```typescript
// vite.config.ts
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import { ssg } from '@augment/vite-plugin-ssg'

export default defineConfig({
  plugins: [
    react(),
    ssg({
      // Plugin configuration
      entry: 'src/App.tsx',
      outDir: 'dist',
      hydration: true
    })
  ]
})
```

### 2. Create Your App

```typescript
// src/App.tsx
import { Hydrate } from '@augment/vite-plugin-ssg/client'

export default function App() {
  return (
    <div>
      <h1>Welcome to SSG</h1>
      <Hydrate>
        <InteractiveComponent />
      </Hydrate>
    </div>
  )
}

function InteractiveComponent() {
  const [count, setCount] = useState(0)
  return (
    <button onClick={() => setCount(c => c + 1)}>
      Count: {count}
    </button>
  )
}
```

### 3. Build

```bash
npm run build
```

## Example

Check out the [example](./example) directory for a complete working example that demonstrates:

- Single-page application structure
- Server-side rendering
- Selective client-side hydration
- React Server Components
- Interactive components
- Mixed static and dynamic content

To run the example:

```bash
cd example
npm install
npm run build
npm run preview
```

## Documentation

For detailed documentation, examples, and API reference, see the [docs](./docs) directory.

## License

MIT
