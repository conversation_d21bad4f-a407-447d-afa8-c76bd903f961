{"compilerOptions": {"target": "ES2022", "lib": ["ES2022", "DOM", "DOM.Iterable"], "module": "ESNext", "skipLibCheck": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "moduleResolution": "bundler"}, "include": ["src/**/*", "vite.config.ts"], "exclude": ["node_modules", "dist"]}