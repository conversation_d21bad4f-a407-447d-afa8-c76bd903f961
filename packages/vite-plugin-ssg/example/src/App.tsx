import React, { useState } from 'react'
import { Hydrate, withHydration } from '@augment/vite-plugin-ssg/client'

export default function App() {
  console.log('hi');
  return (
    <div style={{ padding: '2rem', fontFamily: 'system-ui', maxWidth: '800px', margin: '0 auto' }}>
      <header style={{ marginBottom: '3rem', textAlign: 'center' }}>
        <h1 style={{ color: '#007acc', marginBottom: '0.5rem' }}>
          SSG with React Server Components
        </h1>
        <p style={{ color: '#666', fontSize: '1.1em' }}>
          A single-page application with selective hydration
        </p>
      </header>

      <main>
        <section style={{ marginBottom: '3rem' }}>
          <h2>Static Content</h2>
          <p>
            This content is rendered on the server at build time and sent as static HTML.
            No JavaScript is needed for this content, making it fast to load and SEO-friendly.
          </p>
          <div style={{
            padding: '1rem',
            backgroundColor: '#f8f9fa',
            borderRadius: '8px',
            border: '1px solid #e9ecef'
          }}>
            <h3>Benefits of Static Generation</h3>
            <ul>
              <li>⚡ Fast loading times</li>
              <li>🔍 Excellent SEO performance</li>
              <li>🛡️ Enhanced security</li>
              <li>📈 Better scalability</li>
            </ul>
          </div>
        </section>

        <section style={{ marginBottom: '3rem' }}>
          <h2>Interactive Content</h2>
          <p>
            The components below are hydrated on the client side, allowing for interactivity
            while preserving the server-rendered markup.
          </p>

          <div style={{
            display: 'grid',
            gap: '1.5rem',
            gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
            marginTop: '1.5rem'
          }}>
            <div style={{
              padding: '1.5rem',
              border: '1px solid #007acc',
              borderRadius: '8px',
              backgroundColor: '#f0f8ff'
            }}>
              <h3>Manual Hydration</h3>
              <p>This component uses the Hydrate wrapper:</p>
                <Counter />
            </div>

            <div style={{
              padding: '1.5rem',
              border: '1px solid #28a745',
              borderRadius: '8px',
              backgroundColor: '#f8fff8'
            }}>
              <h3>HOC Hydration</h3>
              <p>This component uses withHydration HOC:</p>
              <HydratedToggle />
            </div>
          </div>
        </section>

        <section style={{ marginBottom: '3rem' }}>
          <h2>Complex Interactive Example</h2>
          <p>
            A more complex example showing multiple interactive features:
          </p>

          <Hydrate id="todo-app" fallback={<div>Loading todo app...</div>}>
            <TodoApp />
          </Hydrate>
        </section>

        <section>
          <h2>About This Example</h2>
          <div style={{
            padding: '1.5rem',
            backgroundColor: '#fff3cd',
            border: '1px solid #ffeaa7',
            borderRadius: '8px'
          }}>
            <h3>How It Works</h3>
            <p>
              This single-page application demonstrates the power of combining static site generation
              with selective hydration:
            </p>
            <ol>
              <li>The entire page is pre-rendered at build time</li>
              <li>Static content loads instantly without JavaScript</li>
              <li>Interactive components are hydrated selectively on the client</li>
              <li>Users get the best of both worlds: speed and interactivity</li>
            </ol>
          </div>
        </section>
      </main>

      <footer style={{
        marginTop: '4rem',
        padding: '2rem 0',
        borderTop: '1px solid #eee',
        textAlign: 'center',
        color: '#666'
      }}>
        <p>Built with @augment/vite-plugin-ssg</p>
      </footer>
    </div>
  )
}

// Interactive components
function Counter() {
  const [count, setCount] = useState(0)

  return (
    <div>
      <p style={{ fontSize: '1.2em', marginBottom: '1rem' }}>
        Count: <strong>{count}</strong>
      </p>
      <div style={{ display: 'flex', gap: '0.5rem' }}>
        <button
          onClick={() => setCount(c => c + 1)}
          style={{
            padding: '0.5rem 1rem',
            backgroundColor: '#007acc',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          +
        </button>
        <button
          onClick={() => setCount(c => c - 1)}
          style={{
            padding: '0.5rem 1rem',
            backgroundColor: '#dc3545',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          -
        </button>
        <button
          onClick={() => setCount(0)}
          style={{
            padding: '0.5rem 1rem',
            backgroundColor: '#6c757d',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          Reset
        </button>
      </div>
    </div>
  )
}

const HydratedToggle = withHydration(
  () => {
    const [isOn, setIsOn] = useState(false)

    return (
      <div>
        <p style={{ fontSize: '1.2em', marginBottom: '1rem' }}>
          Status: <strong>{isOn ? 'ON' : 'OFF'}</strong>
        </p>
        <button
          onClick={() => setIsOn(!isOn)}
          style={{
            padding: '0.5rem 1.5rem',
            backgroundColor: isOn ? '#28a745' : '#6c757d',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer',
            transition: 'background-color 0.2s'
          }}
        >
          Toggle
        </button>
      </div>
    )
  },
  {
    fallback: <div>Loading toggle...</div>
  }
)

function TodoApp() {
  const [todos, setTodos] = useState([
    { id: 1, text: 'Learn about SSG', completed: true },
    { id: 2, text: 'Try selective hydration', completed: false }
  ])
  const [newTodo, setNewTodo] = useState('')

  const addTodo = () => {
    if (newTodo.trim()) {
      setTodos([...todos, {
        id: Date.now(),
        text: newTodo.trim(),
        completed: false
      }])
      setNewTodo('')
    }
  }

  const toggleTodo = (id: number) => {
    setTodos(todos.map(todo =>
      todo.id === id ? { ...todo, completed: !todo.completed } : todo
    ))
  }

  const deleteTodo = (id: number) => {
    setTodos(todos.filter(todo => todo.id !== id))
  }

  return (
    <div style={{
      padding: '1.5rem',
      border: '1px solid #ddd',
      borderRadius: '8px',
      backgroundColor: '#fafafa'
    }}>
      <h3 style={{ marginTop: 0 }}>Todo List</h3>

      <div style={{ display: 'flex', gap: '0.5rem', marginBottom: '1rem' }}>
        <input
          type="text"
          value={newTodo}
          onChange={(e) => setNewTodo(e.target.value)}
          onKeyPress={(e) => e.key === 'Enter' && addTodo()}
          placeholder="Add a new todo..."
          style={{
            flex: 1,
            padding: '0.5rem',
            border: '1px solid #ccc',
            borderRadius: '4px'
          }}
        />
        <button
          onClick={addTodo}
          style={{
            padding: '0.5rem 1rem',
            backgroundColor: '#007acc',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          Add
        </button>
      </div>

      <ul style={{ listStyle: 'none', padding: 0 }}>
        {todos.map(todo => (
          <li
            key={todo.id}
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '0.5rem',
              padding: '0.5rem',
              marginBottom: '0.5rem',
              backgroundColor: 'white',
              borderRadius: '4px',
              border: '1px solid #eee'
            }}
          >
            <input
              type="checkbox"
              checked={todo.completed}
              onChange={() => toggleTodo(todo.id)}
            />
            <span
              style={{
                flex: 1,
                textDecoration: todo.completed ? 'line-through' : 'none',
                color: todo.completed ? '#666' : 'inherit'
              }}
            >
              {todo.text}
            </span>
            <button
              onClick={() => deleteTodo(todo.id)}
              style={{
                padding: '0.25rem 0.5rem',
                backgroundColor: '#dc3545',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer',
                fontSize: '0.8em'
              }}
            >
              Delete
            </button>
          </li>
        ))}
      </ul>

      {todos.length === 0 && (
        <p style={{ textAlign: 'center', color: '#666', fontStyle: 'italic' }}>
          No todos yet. Add one above!
        </p>
      )}
    </div>
  )
}
