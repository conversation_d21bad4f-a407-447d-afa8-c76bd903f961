import React from 'react'
import { Hydrate } from '@augment/vite-plugin-ssg/client'
import type { SSGRoute } from '@augment/vite-plugin-ssg'

interface BlogPostProps {
  title: string
  content: string
  date: string
  slug: string
}

export default function BlogPost({ title, content, date, slug }: BlogPostProps) {
  return (
    <div style={{ padding: '2rem', fontFamily: 'system-ui', maxWidth: '800px', margin: '0 auto' }}>
      <article>
        <header style={{ marginBottom: '2rem', borderBottom: '1px solid #eee', paddingBottom: '1rem' }}>
          <h1 style={{ marginBottom: '0.5rem' }}>{title}</h1>
          <p style={{ color: '#666', fontSize: '0.9em' }}>
            Published on {date}
          </p>
        </header>
        
        <div 
          style={{ lineHeight: '1.6', fontSize: '1.1em' }}
          dangerouslySetInnerHTML={{ __html: content }} 
        />
        
        <footer style={{ marginTop: '3rem', padding: '1rem', backgroundColor: '#f8f9fa', borderRadius: '8px' }}>
          <h3>Engage with this post</h3>
          <p>Like what you read? Let us know!</p>
          
          <Hydrate fallback={<div>Loading engagement tools...</div>}>
            <EngagementTools slug={slug} />
          </Hydrate>
        </footer>
      </article>
      
      <nav style={{ marginTop: '2rem', padding: '1rem', borderTop: '1px solid #eee' }}>
        <a href="/blog" style={{ color: '#007acc', textDecoration: 'none', marginRight: '1rem' }}>
          ← Back to Blog
        </a>
        <a href="/" style={{ color: '#007acc', textDecoration: 'none' }}>
          Home
        </a>
      </nav>
    </div>
  )
}

function EngagementTools({ slug }: { slug: string }) {
  const [liked, setLiked] = React.useState(false)
  const [likes, setLikes] = React.useState(Math.floor(Math.random() * 50) + 10)
  
  const handleLike = () => {
    if (!liked) {
      setLikes(prev => prev + 1)
      setLiked(true)
    } else {
      setLikes(prev => prev - 1)
      setLiked(false)
    }
  }
  
  return (
    <div style={{ display: 'flex', gap: '1rem', alignItems: 'center' }}>
      <button
        onClick={handleLike}
        style={{
          padding: '0.5rem 1rem',
          backgroundColor: liked ? '#dc3545' : '#007acc',
          color: 'white',
          border: 'none',
          borderRadius: '4px',
          cursor: 'pointer'
        }}
      >
        {liked ? '❤️ Liked' : '🤍 Like'} ({likes})
      </button>
      
      <button
        onClick={() => {
          const url = `${window.location.origin}/blog/${slug}`
          navigator.clipboard.writeText(url)
          alert('Link copied to clipboard!')
        }}
        style={{
          padding: '0.5rem 1rem',
          backgroundColor: '#28a745',
          color: 'white',
          border: 'none',
          borderRadius: '4px',
          cursor: 'pointer'
        }}
      >
        📋 Share
      </button>
    </div>
  )
}

// Generate static paths for dynamic routes
export async function getStaticPaths(): Promise<SSGRoute[]> {
  return [
    { path: '/blog/hello-world', params: { slug: 'hello-world' } },
    { path: '/blog/getting-started', params: { slug: 'getting-started' } }
  ]
}

// Get static props for each route
export async function getStaticProps(route: SSGRoute) {
  const { slug } = route.params || {}
  
  // In a real app, this would fetch from a CMS or markdown files
  const posts: Record<string, Omit<BlogPostProps, 'slug'>> = {
    'hello-world': {
      title: 'Hello World',
      content: `
        <p>Welcome to our blog! This is our very first post, and we're excited to share our journey with you.</p>
        <p>This blog is built using static site generation with React Server Components, which means:</p>
        <ul>
          <li>Fast loading times</li>
          <li>Great SEO performance</li>
          <li>Selective hydration for interactive features</li>
        </ul>
        <p>We hope you enjoy reading our content!</p>
      `,
      date: '2024-01-01'
    },
    'getting-started': {
      title: 'Getting Started with SSG',
      content: `
        <p>Static Site Generation (SSG) is a powerful technique for building fast, SEO-friendly websites.</p>
        <h2>What is SSG?</h2>
        <p>SSG pre-renders pages at build time, creating static HTML files that can be served quickly from a CDN.</p>
        <h2>Benefits of SSG</h2>
        <ul>
          <li><strong>Performance:</strong> Static files load incredibly fast</li>
          <li><strong>SEO:</strong> Search engines can easily crawl pre-rendered content</li>
          <li><strong>Security:</strong> No server-side vulnerabilities</li>
          <li><strong>Scalability:</strong> Easy to scale with CDNs</li>
        </ul>
        <h2>React Server Components</h2>
        <p>With React Server Components, you can run components on the server during build time, reducing the JavaScript bundle size sent to the client.</p>
      `,
      date: '2024-01-02'
    }
  }
  
  const post = posts[slug as string]
  if (!post) {
    throw new Error(`Post not found: ${slug}`)
  }
  
  return {
    ...post,
    slug
  }
}
