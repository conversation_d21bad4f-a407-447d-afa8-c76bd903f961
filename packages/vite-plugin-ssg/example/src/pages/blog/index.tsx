import React from 'react'

export default function BlogPage() {
  const posts = [
    {
      slug: 'hello-world',
      title: 'Hello World',
      excerpt: 'Welcome to our blog! This is our first post.',
      date: '2024-01-01'
    },
    {
      slug: 'getting-started',
      title: 'Getting Started with SSG',
      excerpt: 'Learn how to build static sites with React Server Components.',
      date: '2024-01-02'
    }
  ]

  return (
    <div style={{ padding: '2rem', fontFamily: 'system-ui' }}>
      <h1>Blog</h1>
      <p>Welcome to our blog! Here are our latest posts:</p>
      
      <div style={{ marginTop: '2rem' }}>
        {posts.map(post => (
          <article 
            key={post.slug}
            style={{ 
              marginBottom: '2rem', 
              padding: '1.5rem', 
              border: '1px solid #ddd', 
              borderRadius: '8px',
              backgroundColor: '#fafafa'
            }}
          >
            <h2 style={{ marginTop: 0 }}>
              <a 
                href={`/blog/${post.slug}`}
                style={{ color: '#007acc', textDecoration: 'none' }}
              >
                {post.title}
              </a>
            </h2>
            <p style={{ color: '#666', fontSize: '0.9em', marginBottom: '1rem' }}>
              Published on {post.date}
            </p>
            <p>{post.excerpt}</p>
            <a 
              href={`/blog/${post.slug}`}
              style={{ color: '#007acc', textDecoration: 'none', fontWeight: 'bold' }}
            >
              Read more →
            </a>
          </article>
        ))}
      </div>

      <div style={{ marginTop: '2rem' }}>
        <a href="/" style={{ color: '#007acc', textDecoration: 'none' }}>
          ← Back to Home
        </a>
      </div>
    </div>
  )
}
