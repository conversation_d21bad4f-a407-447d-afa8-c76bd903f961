import React from 'react'
import { Hydrate, withHydration } from '@augment/vite-plugin-ssg/client'

export default function AboutPage() {
  return (
    <div style={{ padding: '2rem', fontFamily: 'system-ui' }}>
      <h1>About This Example</h1>
      
      <div style={{ marginBottom: '2rem' }}>
        <h2>Static Site Generation with React Server Components</h2>
        <p>
          This example demonstrates how to use the @augment/vite-plugin-ssg plugin 
          to create static sites with React Server Components and selective hydration.
        </p>
      </div>

      <div style={{ marginBottom: '2rem' }}>
        <h2>Features Demonstrated</h2>
        <ul>
          <li>Server-side rendering at build time</li>
          <li>Selective client-side hydration</li>
          <li>File-based routing</li>
          <li>Static and interactive content mixing</li>
        </ul>
      </div>

      <div style={{ marginBottom: '2rem', padding: '1rem', border: '1px solid #28a745', borderRadius: '8px' }}>
        <h3>Hydrated Component with HOC</h3>
        <p>This component uses the withHydration higher-order component:</p>
        <HydratedGreeting name="Developer" />
      </div>

      <div style={{ marginBottom: '2rem', padding: '1rem', border: '1px solid #ffc107', borderRadius: '8px' }}>
        <h3>Manual Hydration</h3>
        <p>This component uses manual hydration with the Hydrate wrapper:</p>
        <Hydrate id="manual-greeting">
          <InteractiveGreeting />
        </Hydrate>
      </div>

      <div style={{ marginTop: '2rem' }}>
        <a href="/" style={{ color: '#007acc', textDecoration: 'none' }}>
          ← Back to Home
        </a>
      </div>
    </div>
  )
}

// Component using withHydration HOC
const HydratedGreeting = withHydration(
  ({ name }: { name: string }) => {
    const [clicked, setClicked] = React.useState(false)
    
    return (
      <div>
        <p>Hello, {name}! {clicked && '👋'}</p>
        <button 
          onClick={() => setClicked(!clicked)}
          style={{ padding: '0.5rem 1rem' }}
        >
          {clicked ? 'Hide' : 'Show'} Wave
        </button>
      </div>
    )
  },
  {
    fallback: <div>Loading greeting...</div>
  }
)

// Component using manual hydration
function InteractiveGreeting() {
  const [message, setMessage] = React.useState('Click the button!')
  
  const messages = [
    'Hello there! 👋',
    'How are you doing? 😊',
    'Nice to meet you! 🎉',
    'Welcome to SSG! 🚀',
    'Click the button!'
  ]
  
  const nextMessage = () => {
    const currentIndex = messages.indexOf(message)
    const nextIndex = (currentIndex + 1) % messages.length
    setMessage(messages[nextIndex])
  }
  
  return (
    <div>
      <p style={{ fontSize: '1.2em', marginBottom: '1rem' }}>{message}</p>
      <button 
        onClick={nextMessage}
        style={{ padding: '0.5rem 1rem', backgroundColor: '#007acc', color: 'white', border: 'none', borderRadius: '4px' }}
      >
        Next Message
      </button>
    </div>
  )
}
