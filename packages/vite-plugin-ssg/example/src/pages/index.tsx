import React, { useState } from 'react'
import { Hydrate } from '@augment/vite-plugin-ssg/client'

export default function HomePage() {
  return (
    <div style={{ padding: '2rem', fontFamily: 'system-ui' }}>
      <h1>Welcome to SSG with React Server Components</h1>
      <p>This content is server-rendered at build time!</p>
      
      <div style={{ marginTop: '2rem', padding: '1rem', border: '1px solid #ccc', borderRadius: '8px' }}>
        <h2>Static Content</h2>
        <p>This paragraph is rendered on the server and sent as static HTML.</p>
        <p>No JavaScript is needed for this content.</p>
      </div>

      <div style={{ marginTop: '2rem', padding: '1rem', border: '1px solid #007acc', borderRadius: '8px' }}>
        <h2>Interactive Content</h2>
        <p>The component below will be hydrated on the client:</p>
        
        <Hydrate fallback={<div>Loading interactive content...</div>}>
          <Counter />
        </Hydrate>
      </div>

      <div style={{ marginTop: '2rem' }}>
        <h2>Navigation</h2>
        <ul>
          <li><a href="/about">About Page</a></li>
          <li><a href="/blog">Blog</a></li>
        </ul>
      </div>
    </div>
  )
}

function Counter() {
  const [count, setCount] = useState(0)
  
  return (
    <div style={{ padding: '1rem', backgroundColor: '#f0f8ff', borderRadius: '4px' }}>
      <h3>Interactive Counter</h3>
      <p>Current count: <strong>{count}</strong></p>
      <div style={{ marginTop: '1rem' }}>
        <button 
          onClick={() => setCount(c => c + 1)}
          style={{ marginRight: '0.5rem', padding: '0.5rem 1rem' }}
        >
          Increment
        </button>
        <button 
          onClick={() => setCount(c => c - 1)}
          style={{ marginRight: '0.5rem', padding: '0.5rem 1rem' }}
        >
          Decrement
        </button>
        <button 
          onClick={() => setCount(0)}
          style={{ padding: '0.5rem 1rem' }}
        >
          Reset
        </button>
      </div>
      <p style={{ marginTop: '1rem', fontSize: '0.9em', color: '#666' }}>
        This component is hydrated on the client and maintains state.
      </p>
    </div>
  )
}
