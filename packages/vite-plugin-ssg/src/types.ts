import type { Plugin } from 'vite'

export interface SSGConfig {
  /** Entry component file (default: 'src/App.tsx') */
  entry?: string
  /** Output directory for generated files (default: 'dist') */
  outDir?: string
  /** Custom HTML template */
  template?: string
  /** Whether to enable client-side hydration (default: true) */
  hydration?: boolean
  /** Base URL for the site (default: '/') */
  base?: string
  /** Custom render function for the app */
  render?: (component: React.ComponentType) => Promise<string> | string
}

export interface SSGContext {
  config: Required<SSGConfig>
  isDev: boolean
  isBuild: boolean
}

export interface AppModule {
  default: React.ComponentType<any>
}

export interface RenderedPage {
  html: string
  assets: string[]
}

export interface HydrationManifest {
  components: string[]
  chunks: string[]
}

export type SSGPlugin = (config?: SSGConfig) => Plugin[]
