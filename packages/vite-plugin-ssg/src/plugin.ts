import type { Plugin } from 'vite'
import { resolve, dirname } from 'node:path'
import { fileURLToPath } from 'node:url'
import type { SSGContext, SSGPlugin } from './types.js'
import { resolveConfig, createContext } from './config.js'
import { generatePage } from './generator.js'

export const ssg: SSGPlugin = (userConfig = {}) => {
  const config = resolveConfig(userConfig)
  let context: SSGContext
  const __dirname = dirname(fileURLToPath(import.meta.url))

  const mainPlugin: Plugin = {
    name: 'vite-plugin-ssg',
    configResolved(resolvedConfig) {
      const isDev = resolvedConfig.command === 'serve'
      const isBuild = resolvedConfig.command === 'build'
      context = createContext(config, isDev, isBuild)
    },
    
    configureServer(devServer) {
      // Add middleware to serve the app during development
      devServer.middlewares.use('/', async (req, res, next) => {
        if (req.url === '/' || req.url === '/index.html') {
          try {
            // Create HTML template
            let html = `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>SSG App - Dev Mode</title>
  <base href="${context.config.base}">
</head>
<body>
  <div id="root"></div>
  <script type="module">
    import React from 'react'
    import { createRoot } from 'react-dom/client'

    // Import the app component
    import App from '/${context.config.entry}'

    // Render the app
    const root = createRoot(document.getElementById('root'))
    root.render(React.createElement(App))
  </script>
</body>
</html>`

            // Transform the HTML through Vite's transform pipeline
            html = await devServer.transformIndexHtml(req.url, html)

            res.setHeader('Content-Type', 'text/html')
            res.end(html)
          } catch (error) {
            console.error('[SSG] Dev server error:', error)
            next()
          }
        } else {
          next()
        }
      })
    },

    async generateBundle() {
      if (context.isBuild) {
        // Generate static page
        await generatePage(context, this)
      }
    },

    resolveId(id) {
      // Handle virtual modules
      if (id === 'virtual:ssg/context') {
        return id
      }
    },

    load(id) {
      if (id === 'virtual:ssg/context') {
        return `export default ${JSON.stringify(context)}`
      }
    }
  }

  const clientPlugin: Plugin = {
    name: 'vite-plugin-ssg:client',
    apply: 'build',
    config(config) {
      // Ensure client build includes hydration runtime
      if (!config.build) config.build = {}
      if (!config.build.rollupOptions) config.build.rollupOptions = {}
      if (!config.build.rollupOptions.input) {
        config.build.rollupOptions.input = {}
      }
      
      const input = config.build.rollupOptions.input as Record<string, string>
      input['ssg-client'] = resolve(__dirname, '../dist/client.js')
    }
  }

  return [mainPlugin, clientPlugin]
}
