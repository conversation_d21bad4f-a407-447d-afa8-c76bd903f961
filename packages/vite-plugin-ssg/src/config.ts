import type { SSGConfig, SSGContext } from './types.js'

const DEFAULT_CONFIG: Required<SSGConfig> = {
  entry: 'src/App.tsx',
  outDir: 'dist',
  template: '',
  hydration: true,
  base: '/',
  render: async () => ''
}

export function resolveConfig(userConfig: SSGConfig = {}): Required<SSGConfig> {
  const config = { ...DEFAULT_CONFIG, ...userConfig }
  
  // Normalize base URL
  if (!config.base.startsWith('/')) {
    config.base = '/' + config.base
  }
  if (!config.base.endsWith('/')) {
    config.base = config.base + '/'
  }
  
  return config
}

export function createContext(
  config: Required<SSGConfig>,
  isDev: boolean = false,
  isBuild: boolean = false
): SSGContext {
  return {
    config,
    isDev,
    isBuild
  }
}
