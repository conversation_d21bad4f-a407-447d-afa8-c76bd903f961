import React from 'react'
import { hydrateRoot, createRoot } from 'react-dom/client'

/**
 * Hydration context for managing client-side state
 */
interface HydrationContext {
  components: Map<string, React.ComponentType>
  hydrated: Set<string>
}

/**
 * Global hydration context
 */
let hydrationContext: HydrationContext | null = null

/**
 * Initialize hydration
 */
export function hydrate(): void {
  if (typeof window === 'undefined') {
    console.warn('[SSG] hydrate() called on server side')
    return
  }

  hydrationContext = {
    components: new Map(),
    hydrated: new Set()
  }

  // Find and hydrate all marked components
  hydrateMarkedComponents()
}

/**
 * Mark a component for hydration
 */
export function Hydrate({ 
  children, 
  id, 
  fallback 
}: {
  children: React.ReactNode
  id?: string
  fallback?: React.ReactNode
}): React.ReactElement {
  const hydrateId = id || generateHydrateId()
  
  // On server side, render with hydration markers
  if (typeof window === 'undefined') {
    return React.createElement(
      'div',
      {
        'data-hydrate': hydrateId,
        suppressHydrationWarning: true
      },
      children
    )
  }

  // On client side, handle hydration
  return React.createElement(HydrateClient, {
    id: hydrateId,
    fallback,
    children
  })
}

/**
 * Client-side hydration component
 */
function HydrateClient({
  id,
  children,
  fallback
}: {
  id: string
  children: React.ReactNode
  fallback?: React.ReactNode
}): React.ReactElement {
  const [isHydrated, setIsHydrated] = React.useState(false)
  const containerRef = React.useRef<HTMLDivElement>(null)

  React.useEffect(() => {
    if (!hydrationContext || hydrationContext.hydrated.has(id)) {
      return
    }

    const container = containerRef.current
    if (!container) {
      return
    }

    try {
      // Create React root and hydrate
      hydrateRoot(container, children as React.ReactElement)
      hydrationContext.hydrated.add(id)
      setIsHydrated(true)

      console.log(`[SSG] Hydrated component: ${id}`)
    } catch (error) {
      console.error(`[SSG] Failed to hydrate component: ${id}`, error)

      // Fallback to client-side rendering
      const root = createRoot(container)
      root.render(children as React.ReactElement)
      setIsHydrated(true)
    }
  }, [id, children])

  // Show fallback while hydrating
  if (!isHydrated && fallback) {
    return React.createElement('div', { ref: containerRef }, fallback)
  }

  return React.createElement('div', { ref: containerRef }, children)
}

/**
 * Find and hydrate all components marked for hydration
 */
function hydrateMarkedComponents(): void {
  const elements = document.querySelectorAll('[data-hydrate]')
  
  elements.forEach(element => {
    const hydrateId = element.getAttribute('data-hydrate')
    if (!hydrateId || !hydrationContext) {
      return
    }

    if (hydrationContext.hydrated.has(hydrateId)) {
      return
    }

    try {
      // For now, we'll hydrate with a placeholder
      // In a real implementation, this would load the actual component
      const placeholder = React.createElement('div', {
        children: 'Hydrated component'
      })
      
      hydrateRoot(element as Element, placeholder)
      hydrationContext.hydrated.add(hydrateId)
      
      console.log(`[SSG] Auto-hydrated component: ${hydrateId}`)
    } catch (error) {
      console.error(`[SSG] Failed to auto-hydrate component: ${hydrateId}`, error)
    }
  })
}

/**
 * Generate unique hydration ID
 */
function generateHydrateId(): string {
  return `hydrate-${Math.random().toString(36).substr(2, 9)}`
}

/**
 * Check if component is hydrated
 */
export function isHydrated(id: string): boolean {
  return hydrationContext?.hydrated.has(id) ?? false
}

/**
 * Get hydration context (for debugging)
 */
export function getHydrationContext(): HydrationContext | null {
  return hydrationContext
}

/**
 * Higher-order component for automatic hydration
 */
export function withHydration<P extends object>(
  Component: React.ComponentType<P>,
  options: {
    id?: string
    fallback?: React.ReactNode
  } = {}
): React.ComponentType<P> {
  const WrappedComponent = (props: P) => {
    return React.createElement(
      Hydrate,
      {
        id: options.id,
        fallback: options.fallback,
        children: React.createElement(Component, props)
      }
    )
  }

  WrappedComponent.displayName = `withHydration(${Component.displayName || Component.name})`
  
  return WrappedComponent
}

/**
 * Hook for hydration state
 */
export function useHydration(id?: string): {
  isHydrated: boolean
  hydrate: () => void
} {
  const [isHydrated, setIsHydrated] = React.useState(false)
  
  const hydrateComponent = React.useCallback(() => {
    if (!hydrationContext) {
      console.warn('[SSG] No hydration context available')
      return
    }
    
    const componentId = id || 'anonymous'
    if (!hydrationContext.hydrated.has(componentId)) {
      hydrationContext.hydrated.add(componentId)
      setIsHydrated(true)
    }
  }, [id])
  
  React.useEffect(() => {
    if (hydrationContext && id) {
      setIsHydrated(hydrationContext.hydrated.has(id))
    }
  }, [id])
  
  return {
    isHydrated,
    hydrate: hydrateComponent
  }
}
