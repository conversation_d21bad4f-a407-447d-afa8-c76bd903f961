/**
 * @augment/vite-plugin-ssg
 * 
 * A Vite plugin for Static Site Generation with React Server Components
 * and client-side hydration support.
 */

export { ssg } from './plugin.js'
export type {
  SSGConfig,
  SSGContext,
  AppModule,
  RenderedPage,
  HydrationManifest,
  SSGPlugin
} from './types.js'

export { resolveConfig, createContext } from './config.js'
export { SSGRenderer } from './renderer.js'
export { generatePage } from './generator.js'

// Re-export client utilities for convenience
export type { Hydrate, withHydration, useHydration } from './client.js'
