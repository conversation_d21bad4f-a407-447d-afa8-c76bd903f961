import { writeFile, mkdir } from 'node:fs/promises'
import { join, resolve } from 'node:path'
import { existsSync } from 'node:fs'
import type { PluginContext } from 'rollup'
import type { SSGContext } from './types.js'
import { SSGRenderer } from './renderer.js'

/**
 * Generate static page
 */
export async function generatePage(
  context: SSGContext,
  _pluginContext: PluginContext
): Promise<void> {
  const { config } = context
  const renderer = new SSGRenderer(context)

  console.log(`[SSG] Generating index.html...`)

  // Ensure output directory exists
  const outDir = resolve(process.cwd(), config.outDir)
  await ensureDir(outDir)

  try {
    // Render the app
    const renderedPage = await renderer.renderApp()

    // Generate complete HTML document
    const html = renderer.generateDocument(renderedPage)

    // Write HTML file
    const filePath = join(outDir, 'index.html')
    await writeFile(filePath, html, 'utf-8')

    console.log(`[SSG] Generated: index.html`)

    // Generate additional files
    await generateManifest(context, outDir)
  } catch (error) {
    console.error(`[SSG] Failed to generate page`, error)
    throw error
  }
}



/**
 * Generate hydration manifest
 */
async function generateManifest(context: SSGContext, outDir: string): Promise<void> {
  const { config } = context

  if (!config.hydration) {
    return
  }

  // TODO: Implement hydration manifest generation
  // This would contain information about which components need hydration
  // and their corresponding client-side chunks

  const manifest = {
    components: [], // TODO: Extract hydration components
    chunks: [] // TODO: Extract required chunks
  }

  const manifestPath = join(outDir, 'ssg-manifest.json')
  await writeFile(manifestPath, JSON.stringify(manifest, null, 2), 'utf-8')

  console.log(`[SSG] Generated manifest: ${manifestPath}`)
}

/**
 * Ensure directory exists
 */
async function ensureDir(dir: string): Promise<void> {
  if (!existsSync(dir)) {
    await mkdir(dir, { recursive: true })
  }
}
