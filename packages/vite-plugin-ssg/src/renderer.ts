import React from 'react'
import { renderToString } from 'react-dom/server'
import { resolve } from 'node:path'
import type { SSGContext, AppModule, RenderedPage } from './types.js'

/**
 * Server-side renderer for React components
 */
export class SSGRenderer {
  private context: SSGContext

  constructor(context: SSGContext) {
    this.context = context
  }

  /**
   * Render the app component to HTML string
   */
  async renderApp(): Promise<RenderedPage> {
    try {
      // Load the app component
      const component = await this.loadAppComponent()
      if (!component) {
        throw new Error(`No component found at: ${this.context.config.entry}`)
      }

      // Create React element
      const element = React.createElement(component.default)

      // Render to HTML string
      const html = await this.renderToHTML(element)

      return {
        html,
        assets: [] // TODO: Extract assets from rendered component
      }
    } catch (error) {
      console.error(`[SSG] Failed to render app`, error)
      throw error
    }
  }

  /**
   * Load app component module
   */
  private async loadAppComponent(): Promise<AppModule | null> {
    const componentPath = resolve(process.cwd(), this.context.config.entry)

    try {
      // For now, we'll create a simple placeholder component
      // In a real implementation, this would need to use Vite's module resolution
      // or compile the TypeScript/JSX during the build process
      console.warn(`[SSG] Component loading not fully implemented yet: ${componentPath}`)

      // Return a placeholder component
      return {
        default: () => React.createElement('div', {}, 'SSG Placeholder - Component loading not implemented')
      }
    } catch (error) {
      console.error(`[SSG] Failed to load component: ${componentPath}`, error)
      return null
    }
  }

  /**
   * Render React element to HTML
   */
  private async renderToHTML(element: React.ReactElement): Promise<string> {
    const { config } = this.context

    // Use custom render function if provided
    if (config.render !== DEFAULT_RENDER) {
      return await config.render(element.type as React.ComponentType)
    }

    // Default React SSR
    try {
      return renderToString(element)
    } catch (error) {
      console.error(`[SSG] Failed to render React element`, error)
      throw error
    }
  }

  /**
   * Generate complete HTML document
   */
  generateDocument(renderedPage: RenderedPage): string {
    const { config } = this.context
    const { html, assets } = renderedPage

    // Use custom template if provided
    if (config.template) {
      return this.interpolateTemplate(config.template, {
        html,
        assets,
        base: config.base
      })
    }

    // Default HTML template
    return this.getDefaultTemplate(html, assets)
  }

  /**
   * Interpolate template with variables
   */
  private interpolateTemplate(
    template: string,
    variables: Record<string, any>
  ): string {
    return template.replace(/\{\{(\w+)\}\}/g, (match, key) => {
      return variables[key] || match
    })
  }

  /**
   * Get default HTML template
   */
  private getDefaultTemplate(html: string, assets: string[]): string {
    const { config } = this.context

    return `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>SSG App</title>
  <base href="${config.base}">
  ${assets.map(asset => `<link rel="stylesheet" href="${asset}">`).join('\n  ')}
</head>
<body>
  <div id="root">${html}</div>
  ${config.hydration ? this.getHydrationScript() : ''}
</body>
</html>`
  }

  /**
   * Get hydration script for client-side
   */
  private getHydrationScript(): string {
    return `
  <script type="module">
    import { hydrate } from '${this.context.config.base}ssg-client.js'
    hydrate()
  </script>`
  }
}

/**
 * Default render function placeholder
 */
const DEFAULT_RENDER = async () => ''
