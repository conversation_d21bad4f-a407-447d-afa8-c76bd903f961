import { relative } from 'node:path'
import { existsSync, statSync } from 'node:fs'

/**
 * Utility functions for the SSG plugin
 */

/**
 * Normalize file path to use forward slashes
 */
export function normalizePath(path: string): string {
  return path.replace(/\\/g, '/')
}

/**
 * Check if a path is a directory
 */
export function isDirectory(path: string): boolean {
  try {
    return existsSync(path) && statSync(path).isDirectory()
  } catch {
    return false
  }
}

/**
 * Check if a path is a file
 */
export function isFile(path: string): boolean {
  try {
    return existsSync(path) && statSync(path).isFile()
  } catch {
    return false
  }
}

/**
 * Get relative path from one directory to another
 */
export function getRelativePath(from: string, to: string): string {
  const relativePath = relative(from, to)
  return normalizePath(relativePath)
}

/**
 * Ensure path starts with forward slash
 */
export function ensureLeadingSlash(path: string): string {
  return path.startsWith('/') ? path : '/' + path
}

/**
 * Remove leading slash from path
 */
export function removeLeadingSlash(path: string): string {
  return path.startsWith('/') ? path.slice(1) : path
}

/**
 * Ensure path ends with forward slash
 */
export function ensureTrailingSlash(path: string): string {
  return path.endsWith('/') ? path : path + '/'
}

/**
 * Remove trailing slash from path
 */
export function removeTrailingSlash(path: string): string {
  return path.endsWith('/') ? path.slice(0, -1) : path
}

/**
 * Join URL paths correctly
 */
export function joinPaths(...paths: string[]): string {
  const joined = paths
    .filter(Boolean)
    .map((path, index) => {
      if (index === 0) return removeTrailingSlash(path)
      return removeLeadingSlash(removeTrailingSlash(path))
    })
    .join('/')
  
  return ensureLeadingSlash(joined)
}

/**
 * Convert file extension
 */
export function changeExtension(filePath: string, newExt: string): string {
  const lastDotIndex = filePath.lastIndexOf('.')
  if (lastDotIndex === -1) {
    return filePath + newExt
  }
  return filePath.slice(0, lastDotIndex) + newExt
}

/**
 * Get file extension
 */
export function getExtension(filePath: string): string {
  const lastDotIndex = filePath.lastIndexOf('.')
  return lastDotIndex === -1 ? '' : filePath.slice(lastDotIndex)
}

/**
 * Remove file extension
 */
export function removeExtension(filePath: string): string {
  const lastDotIndex = filePath.lastIndexOf('.')
  return lastDotIndex === -1 ? filePath : filePath.slice(0, lastDotIndex)
}

/**
 * Check if string is a valid URL
 */
export function isValidUrl(string: string): boolean {
  try {
    new URL(string)
    return true
  } catch {
    return false
  }
}

/**
 * Debounce function
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null
  
  return (...args: Parameters<T>) => {
    if (timeout) {
      clearTimeout(timeout)
    }
    
    timeout = setTimeout(() => {
      func(...args)
    }, wait)
  }
}

/**
 * Throttle function
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle = false
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args)
      inThrottle = true
      setTimeout(() => {
        inThrottle = false
      }, limit)
    }
  }
}

/**
 * Deep merge objects
 */
export function deepMerge<T extends Record<string, any>>(
  target: T,
  ...sources: Partial<T>[]
): T {
  if (!sources.length) return target
  const source = sources.shift()
  
  if (isObject(target) && isObject(source)) {
    for (const key in source) {
      if (isObject(source[key])) {
        if (!target[key]) Object.assign(target, { [key]: {} })
        deepMerge(target[key], source[key] as Partial<T[Extract<keyof T, string>]>)
      } else {
        Object.assign(target, { [key]: source[key] })
      }
    }
  }
  
  return deepMerge(target, ...sources)
}

/**
 * Check if value is an object
 */
function isObject(item: any): item is Record<string, any> {
  return item && typeof item === 'object' && !Array.isArray(item)
}
