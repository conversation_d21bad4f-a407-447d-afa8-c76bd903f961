/**
 * Client-side hydration utilities for @augment/vite-plugin-ssg
 */

import React from 'react'

/**
 * Initialize hydration
 */
export function hydrate(): void

/**
 * Component wrapper for selective hydration
 */
export interface HydrateProps {
  /** Child components to hydrate */
  children: React.ReactNode
  /** Unique identifier for the hydration boundary */
  id?: string
  /** Fallback content while hydrating */
  fallback?: React.ReactNode
}

/**
 * Mark a component for hydration
 */
export function Hydrate(props: HydrateProps): React.ReactElement

/**
 * Higher-order component for automatic hydration
 */
export function withHydration<P extends object>(
  Component: React.ComponentType<P>,
  options?: {
    id?: string
    fallback?: React.ReactNode
  }
): React.ComponentType<P>

/**
 * Hook for hydration state management
 */
export function useHydration(id?: string): {
  isHydrated: boolean
  hydrate: () => void
}

/**
 * Check if a component is hydrated
 * @param id - The hydration ID to check
 */
export function isHydrated(id: string): boolean

/**
 * Get the current hydration context (for debugging)
 */
export function getHydrationContext(): {
  components: Map<string, React.ComponentType>
  hydrated: Set<string>
} | null
