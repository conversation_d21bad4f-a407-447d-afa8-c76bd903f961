{"name": "Augment-UI---Design-Token-Exporte", "version": "1.0.0", "description": "Your Figma Plugin", "main": "code.js", "scripts": {"build": "tsc -p tsconfig.json", "lint": "eslint --ext .ts,.tsx --ignore-pattern node_modules .", "lint:fix": "eslint --ext .ts,.tsx --ignore-pattern node_modules --fix .", "watch": "npm run build -- --watch"}, "author": "", "license": "", "devDependencies": {"@figma/eslint-plugin-figma-plugins": "*", "@figma/plugin-typings": "*", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "eslint": "^8.54.0", "typescript": "^5.3.2"}, "eslintConfig": {"extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended", "plugin:@figma/figma-plugins/recommended"], "parser": "@typescript-eslint/parser", "parserOptions": {"project": "./tsconfig.json"}, "root": true, "rules": {"@typescript-eslint/no-unused-vars": ["error", {"argsIgnorePattern": "^_", "varsIgnorePattern": "^_", "caughtErrorsIgnorePattern": "^_"}]}}}