# Augment UI Workspace

This document describes the npm workspace structure for the Augment UI project.

## Workspace Structure

```
augment-ui/
├── packages/
│   ├── ui/                    # @augment/ui - Component library
│   │   ├── src/              # Source code
│   │   │   ├── components/   # React components
│   │   │   ├── theme/        # Design system
│   │   │   ├── utils/        # Utilities
│   │   │   └── styles/       # Global styles
│   │   ├── dist/             # Built library
│   │   ├── package.json      # UI package config
│   │   ├── tsconfig.json     # TypeScript config
│   │   ├── vite.config.ts    # Build config
│   │   └── README.md         # UI documentation
│   │
│   └── demo/                 # @augment/demo - Demo application
│       ├── src/              # Demo source code
│       ├── dist/             # Built demo
│       ├── package.json      # Demo package config
│       ├── index.html        # HTML entry point
│       ├── vite.config.ts    # Demo build config
│       └── README.md         # Demo documentation
│
├── docs/                     # Documentation
├── package.json              # Root workspace config
├── README.md                 # Main documentation
├── CHANGELOG.md              # Version history
├── CONTRIBUTING.md           # Contribution guide
├── LICENSE                   # License file
└── .gitignore               # Git ignore rules
```

## Package Dependencies

### @augment/ui
- **Purpose**: The main component library
- **Dependencies**: React, Tailwind CSS, TypeScript, class-variance-authority
- **Exports**: Components, themes, utilities
- **Build**: Vite library mode with TypeScript declarations

### @augment/demo
- **Purpose**: Demo application showcasing components
- **Dependencies**: @augment/ui (local), React, Vite
- **Purpose**: Development and testing environment

## Workspace Commands

### Development
```bash
npm run dev          # Start demo application
npm run dev:ui       # Start UI library in watch mode
```

### Building
```bash
npm run build        # Build UI library only
npm run build:demo   # Build demo application
npm run build:all    # Build all packages
```

### Maintenance
```bash
npm run clean        # Clean all packages
npm run test         # Run tests in all packages
npm run typecheck    # Type check all packages
```

## Benefits of Workspace Structure

1. **Separation of Concerns**: Library and demo are separate packages
2. **Independent Versioning**: Each package can have its own version
3. **Shared Dependencies**: Common dependencies are hoisted to root
4. **Development Efficiency**: Easy to work on library and demo together
5. **Publishing**: Only the UI library needs to be published
6. **Testing**: Demo serves as integration test for the library

## Development Workflow

1. **Library Development**: Work in `packages/ui/src/`
2. **Testing**: Use demo app to test components
3. **Building**: Build library with `npm run build`
4. **Publishing**: Publish from `packages/ui/` directory

## Package Relationships

```
@augment/demo  →  depends on  →  @augment/ui
```

The demo package depends on the UI package using a local file reference, ensuring it always uses the latest local version during development.

## Configuration Files

- **Root `package.json`**: Workspace configuration and shared scripts
- **UI `package.json`**: Library-specific configuration and dependencies
- **Demo `package.json`**: Demo-specific configuration and dependencies
- **TypeScript configs**: Separate configs for library and demo
- **Vite configs**: Optimized for library building vs. app development

This workspace structure provides a clean separation between the component library and its demo application while maintaining development efficiency.
