# Component Documentation

## Layout Components

### Box

The most fundamental layout component. A generic container with no default styling.

```tsx
import { Box } from '@augment/ui'

// Basic usage
<Box className="p-4 bg-gray-100">
  Content goes here
</Box>

// Polymorphic usage
<Box as="section" className="max-w-md">
  <h2>Section Title</h2>
</Box>
```

### Flex

Flexbox container with convenient props for common flexbox patterns.

```tsx
import { Flex } from '@augment/ui'

// Horizontal layout with gap
<Flex gap={4} align="center" justify="between">
  <div>Item 1</div>
  <div>Item 2</div>
  <div>Item 3</div>
</Flex>

// Vertical layout
<Flex direction="col" gap={2}>
  <div>Item 1</div>
  <div>Item 2</div>
</Flex>
```

### Stack / HStack

Convenient components for vertical and horizontal stacks.

```tsx
import { Stack, HStack } from '@augment/ui'

// Vertical stack
<Stack spacing={4}>
  <div>Item 1</div>
  <div>Item 2</div>
  <div>Item 3</div>
</Stack>

// Horizontal stack
<HStack spacing={2} align="center">
  <button>Cancel</button>
  <button>Save</button>
</HStack>
```

### Grid

CSS Grid layout with responsive column and row support.

```tsx
import { Grid, GridItem } from '@augment/ui'

// Basic grid
<Grid columns={3} gap={4}>
  <GridItem>Item 1</GridItem>
  <GridItem>Item 2</GridItem>
  <GridItem>Item 3</GridItem>
</Grid>

// Responsive grid with spanning
<Grid columns={4} gap={6}>
  <GridItem colSpan={2}>Wide item</GridItem>
  <GridItem>Item 2</GridItem>
  <GridItem>Item 3</GridItem>
</Grid>
```

## Typography Components

### Text

Versatile text component with semantic variants.

```tsx
import { Text } from '@augment/ui'

// Basic text
<Text>Regular paragraph text</Text>

// Variants
<Text variant="lead">Lead text for introductions</Text>
<Text variant="small">Small text for captions</Text>
<Text variant="muted">Muted text for less important info</Text>

// Sizes and colors
<Text size="lg" color="primary">Large primary text</Text>
<Text weight="bold" color="error">Bold error text</Text>
```

### Heading

Semantic headings with customizable visual appearance.

```tsx
import { Heading } from '@augment/ui'

// Semantic headings
<Heading as="h1">Page Title</Heading>
<Heading as="h2">Section Title</Heading>

// Visual size different from semantic level
<Heading as="h3" size="h1">Visually large h3</Heading>

// Custom styling
<Heading as="h2" color="primary" weight="normal">
  Custom styled heading
</Heading>
```

### Typography

Material UI-style typography component with variant and component props.

```tsx
import { Typography } from '@augment/ui'

// Display variants
<Typography variant="h1">Main Title</Typography>
<Typography variant="h2">Section Title</Typography>
<Typography variant="h3">Subsection</Typography>

// Body text
<Typography variant="body1">
  Default body text with comfortable line height
</Typography>
<Typography variant="body2">
  Smaller body text for secondary content
</Typography>

// Subtitles
<Typography variant="subtitle1">Medium emphasis subtitle</Typography>
<Typography variant="subtitle2">Smaller subtitle</Typography>

// Special variants
<Typography variant="caption">Small caption text</Typography>
<Typography variant="overline">UPPERCASE OVERLINE</Typography>
<Typography variant="button">Button text styling</Typography>

// Colors
<Typography variant="body1" color="primary">Primary text</Typography>
<Typography variant="body1" color="error">Error text</Typography>
<Typography variant="body1" color="textSecondary">Secondary text</Typography>

// Alignment
<Typography variant="body1" align="center">Centered text</Typography>
<Typography variant="body1" align="right">Right aligned</Typography>

// Custom component
<Typography variant="h4" component="div">
  H4 styling but rendered as div
</Typography>

// Additional props
<Typography variant="body1" gutterBottom>
  Text with bottom margin
</Typography>
<Typography variant="body1" noWrap>
  Text that truncates with ellipsis
</Typography>
<Typography variant="body1" paragraph>
  Forces paragraph element
</Typography>
```

## Form Components

### Button

Interactive buttons with multiple variants and states.

```tsx
import { Button } from '@augment/ui'

// Basic buttons
<Button>Default Button</Button>
<Button variant="destructive">Delete</Button>
<Button variant="outline">Cancel</Button>

// Sizes
<Button size="sm">Small</Button>
<Button size="lg">Large</Button>

// With icons and loading
<Button leftIcon={<PlusIcon />}>Add Item</Button>
<Button loading>Saving...</Button>
```

### Input

Text input with icon support and validation states.

```tsx
import { Input } from '@augment/ui'

// Basic input
<Input placeholder="Enter your name" />

// With icons
<Input 
  leftIcon={<SearchIcon />}
  placeholder="Search..."
/>

// Error state
<Input 
  error
  placeholder="Email address"
  rightIcon={<ErrorIcon />}
/>
```

### FormField

Complete form field with label, input, and error handling.

```tsx
import { FormField, Input } from '@augment/ui'

<FormField
  label="Email Address"
  required
  error="Please enter a valid email"
  helperText="We'll never share your email"
>
  <Input type="email" placeholder="<EMAIL>" />
</FormField>
```

## Feedback Components

### Alert

Alert messages for important information.

```tsx
import { Alert, AlertTitle, AlertDescription } from '@augment/ui'

// Basic alert
<Alert variant="success">
  <AlertTitle>Success!</AlertTitle>
  <AlertDescription>
    Your changes have been saved successfully.
  </AlertDescription>
</Alert>

// Dismissible alert
<Alert variant="warning" dismissible onDismiss={() => console.log('dismissed')}>
  <AlertTitle>Warning</AlertTitle>
  <AlertDescription>
    Please review your input before proceeding.
  </AlertDescription>
</Alert>
```

### Badge

Status and label badges.

```tsx
import { Badge } from '@augment/ui'

// Basic badges
<Badge>Default</Badge>
<Badge variant="success">Active</Badge>
<Badge variant="destructive">Error</Badge>

// Sizes
<Badge size="sm">Small</Badge>
<Badge size="lg">Large</Badge>
```

### Progress

Progress indicators for loading states.

```tsx
import { Progress } from '@augment/ui'

// Determinate progress
<Progress value={75} showValue label="Upload Progress" />

// Indeterminate progress
<Progress indeterminate label="Processing..." />

// Different colors
<Progress value={50} color="success" />
<Progress value={25} color="warning" />
```

## Theming

### Custom Theme

```tsx
import { ThemeProvider, defaultTheme } from '@augment/ui'

const customTheme = {
  ...defaultTheme,
  colors: {
    ...defaultTheme.colors,
    primary: {
      ...defaultTheme.colors.primary,
      500: '#your-brand-color',
    },
  },
}

function App() {
  return (
    <ThemeProvider theme={customTheme}>
      {/* Your app */}
    </ThemeProvider>
  )
}
```

### Using Theme Values

```tsx
import { useTheme, useColorValue } from '@augment/ui'

function MyComponent() {
  const { theme } = useTheme()
  const primaryColor = useColorValue('primary.500')
  
  return (
    <div style={{ color: primaryColor }}>
      Themed content
    </div>
  )
}
```
