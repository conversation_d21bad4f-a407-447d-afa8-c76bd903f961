{"name": "augment-ui-workspace", "version": "0.1.0", "description": "Augment UI component library workspace", "private": true, "type": "module", "workspaces": ["packages/*"], "scripts": {"dev": "npm run dev --workspace=@augment/demo", "dev:ui": "npm run dev --workspace=@augment/ui", "dev:markdown": "npm run dev --workspace=@augment/markdown-renderer", "dev:figma": "npm run dev --workspace=@augment/figma-plugin", "dev:figma-alias": "npm run dev --workspace=@augment/figma-alias-plugin", "dev:ssg": "npm run dev --workspace=@augment/vite-plugin-ssg", "build": "npm run build --workspace=@augment/ui", "build:demo": "npm run build --workspace=@augment/demo", "build:markdown": "npm run build --workspace=@augment/markdown-renderer", "build:figma": "npm run build --workspace=@augment/figma-plugin", "build:figma-alias": "npm run build --workspace=@augment/figma-alias-plugin", "build:ssg": "npm run build --workspace=@augment/vite-plugin-ssg", "build:all": "npm run build --workspaces", "clean": "npm run clean --workspaces", "test": "npm run test --workspaces", "lint": "npm run lint --workspaces", "typecheck": "npm run typecheck --workspaces"}, "author": "<PERSON> <<EMAIL>>", "license": "MIT", "devDependencies": {"@types/node": "^24.0.4", "typescript": "^5.8.3"}}