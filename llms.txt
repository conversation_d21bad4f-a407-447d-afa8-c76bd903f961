# Augment UI - LLM Documentation

## Project Overview

Augment UI is a flexible React component library built with design tokens, Tailwind CSS v4, and TypeScript. It follows patterns similar to Chakra UI with modern styling and accessibility features. The project is organized as an npm workspace monorepo.

## Architecture

### Workspace Structure
```
augment-ui/
├── packages/
│   ├── ui/                    # @augment/ui - Main component library
│   ├── demo/                  # @augment/demo - Demo application
│   ├── vite-plugin-ssg/       # @augment/vite-plugin-ssg - Vite SSG plugin with RSC
│   ├── figma-plugin/          # @augment/figma-plugin - Figma design token exporter
│   └── figma-alias-plugin/    # @augment/figma-alias-plugin - Figma variable alias manager
├── docs/                      # Documentation
├── package.json               # Root workspace configuration
└── llms.txt                   # This file
```

### Component Library (`packages/ui/`)
```
packages/ui/
├── src/
│   ├── components/            # React components organized by category
│   │   ├── layout/           # Box, Flex, Stack, Grid, Card, etc.
│   │   ├── typography/       # Text, Heading, Code, Link
│   │   ├── forms/            # Button, Input, Checkbox, Radio, etc.
│   │   └── feedback/         # Alert, Badge, Spinner, Progress, Skeleton
│   ├── theme/                # Design system and theming
│   │   ├── tokens.ts         # Design tokens (colors, spacing, typography)
│   │   ├── theme.ts          # Theme configuration and utilities
│   │   ├── ThemeProvider.tsx # React context provider for theming
│   │   └── css-vars.ts       # CSS custom properties generation
│   ├── utils/                # Utility functions
│   │   ├── cn.ts             # Class name utility (clsx wrapper)
│   │   └── variants.ts       # Component variants using class-variance-authority
│   ├── styles/               # Global styles and Tailwind configuration
│   │   └── index.css         # Main CSS file with @theme directive
│   └── index.ts              # Main export file
├── dist/                     # Built library output
├── package.json              # UI package configuration
├── tsconfig.json             # TypeScript configuration
├── vite.config.ts            # Vite build configuration
└── postcss.config.js         # PostCSS configuration
```

## Design System

### Design Tokens
The library uses a comprehensive design token system defined in `src/theme/tokens.ts`:

- **Colors**: Primary, secondary, success, warning, error, neutral color scales (50-950)
- **Spacing**: Consistent spacing scale from 0 to 96 (0px to 24rem)
- **Typography**: Font sizes, weights, line heights, and font families
- **Border Radius**: Consistent border radius values (none to 3xl)
- **Shadows**: Elevation shadows (sm to 2xl)
- **Z-Index**: Layering system for modals, dropdowns, tooltips
- **Breakpoints**: Responsive breakpoints (sm, md, lg, xl, 2xl)

### Tailwind CSS v4 Configuration
Uses the modern Tailwind CSS v4 approach with CSS-based configuration:

```css
@import "tailwindcss";
@source "./src/**/*.{js,ts,jsx,tsx}";
@theme {
  --color-primary-500: #3b82f6;
  /* ... other design tokens */
}
```

### Theme System
- **ThemeProvider**: React context provider for theme management
- **CSS Custom Properties**: Automatic generation from design tokens
- **Theme Utilities**: Helper functions for accessing theme values
- **Semantic Tokens**: Component-specific color and size mappings

## Component Architecture

### Base Components
All components follow consistent patterns:

1. **TypeScript Interfaces**: Comprehensive prop types with JSDoc documentation
2. **Forwarded Refs**: All components use `React.forwardRef`
3. **Polymorphic Components**: Many components support `as` prop for element type
4. **Variant System**: Uses class-variance-authority for consistent styling
5. **Accessibility**: ARIA attributes and keyboard navigation support

### Component Categories

#### Layout Components
- **Box**: Fundamental layout component with polymorphic `as` prop
- **Flex**: Flexbox container with convenient props (direction, justify, align, gap)
- **Stack/HStack**: Vertical/horizontal stacks with consistent spacing
- **Grid/GridItem**: CSS Grid layouts with responsive spanning
- **Container**: Responsive container with max-width constraints
- **Card**: Content container with header, title, description, content, footer
- **Divider**: Visual content separator with labels
- **Spacer**: Flexible spacing component
- **Center**: Center content horizontally and vertically

#### Typography Components
- **Text**: Text component with semantic variants (p, lead, small, muted)
- **Heading**: Semantic headings (h1-h6) with customizable visual sizes
- **Typography**: Material UI-style component with variant and component props
- **Code**: Inline and block code display
- **Link**: Styled links with external link indicators

#### Form Components
- **Button**: Interactive buttons with variants and loading states
- **Input**: Text input with icon support and validation states
- **Textarea**: Multi-line text input with auto-resize
- **Checkbox**: Checkbox input with labels and indeterminate state
- **Radio/RadioGroup**: Radio button inputs with group management
- **Label**: Form field labels with required indicators
- **FormField**: Complete form field wrapper with validation

#### Feedback Components
- **Alert**: Alert messages with variants and dismissible option
- **Badge**: Status and label badges
- **Spinner**: Loading spinners with customizable appearance
- **Progress**: Progress bars with determinate/indeterminate states
- **Skeleton**: Loading placeholders for content

## Styling System

### Class Variance Authority
Components use `cva` for variant management:

```typescript
const buttonVariants = cva(
  "base-classes",
  {
    variants: {
      variant: {
        default: "bg-primary-500 text-white",
        outline: "border border-neutral-200"
      },
      size: {
        sm: "h-8 px-3 text-sm",
        md: "h-10 px-4 text-sm"
      }
    },
    defaultVariants: {
      variant: "default",
      size: "md"
    }
  }
)
```

### Utility Functions
- **cn()**: Class name utility wrapping clsx for conditional classes
- **CSS Variables**: Automatic generation from design tokens
- **Theme Utilities**: Functions for accessing theme values programmatically

## Build System

### Vite Configuration
- **Library Mode**: Builds ESM and UMD bundles
- **TypeScript**: Generates declaration files
- **CSS Extraction**: Separate CSS bundle
- **External Dependencies**: React/ReactDOM as peer dependencies

### Package Configuration
- **Dual Package**: ESM and UMD formats
- **Type Definitions**: Comprehensive TypeScript declarations
- **CSS Export**: Separate styles export for consumers
- **Tree Shaking**: Optimized for dead code elimination

## Figma Plugin

### Design Token Exporter
The Figma plugin exports Figma variables as design tokens following the Design Tokens Format Module specification:

```
packages/figma-plugin/
├── src/
│   ├── components/           # React UI components for plugin interface
│   ├── types/               # Design token type definitions
│   ├── utils/               # Token conversion utilities
│   ├── styles/              # Plugin UI styles
│   ├── plugin.ts            # Main plugin logic (Figma API)
│   ├── ui.tsx               # UI entry point (React)
│   └── ui.html              # HTML template
├── dist/                    # Built plugin files
├── manifest.json            # Figma plugin manifest
└── package.json             # Plugin package config
```

### Features
- **Variable Export**: Converts Figma variables to standardized design tokens
- **Design Tokens Format**: Follows W3C Design Tokens Format Module specification
- **Selective Export**: Choose which variable collections to include
- **Token Types**: Supports colors, dimensions, typography, numbers, strings, booleans
- **Metadata**: Includes descriptions and Figma-specific extensions
- **Multiple Outputs**: Copy to clipboard or download JSON

### Token Conversion
- **Colors**: RGBA → hex values with `$type: "color"`
- **Dimensions**: Float variables → pixel values with `$type: "dimension"`
- **Typography**: Font sizes, families, weights with appropriate `$type`
- **Numbers**: Numeric variables with `$type: "number"`
- **Nested Structure**: Supports hierarchical token organization

## Vite SSG Plugin (`packages/vite-plugin-ssg/`)

### Static Site Generation with React Server Components
The Vite SSG plugin provides static site generation capabilities with React Server Components and selective client-side hydration:

```
packages/vite-plugin-ssg/
├── src/
│   ├── plugin.ts             # Main Vite plugin implementation
│   ├── config.ts             # Configuration system and defaults
│   ├── types.ts              # TypeScript type definitions
│   ├── routes.ts             # Route discovery and matching
│   ├── renderer.ts           # Server-side rendering engine
│   ├── generator.ts          # Static page generation
│   ├── client.ts             # Client-side hydration runtime
│   ├── utils.ts              # Utility functions
│   └── index.ts              # Main export file
├── docs/                     # Comprehensive documentation
├── client.d.ts               # Client-side type definitions
├── dist/                     # Built plugin output
├── package.json              # Plugin package configuration
├── tsconfig.json             # TypeScript configuration
├── vite.config.ts            # Vite build configuration
└── README.md                 # Plugin overview and quick start
```

### Features
- **Static Site Generation**: Pre-render React applications at build time
- **React Server Components**: Full RSC support during SSG process
- **Selective Hydration**: Hydrate only interactive components on the client
- **File-based Routing**: Automatic route discovery from file system
- **Dynamic Routes**: Support for parameterized routes with static path generation
- **Custom Templates**: Configurable HTML templates for generated pages
- **Sitemap Generation**: Automatic sitemap.xml generation
- **TypeScript Support**: Comprehensive TypeScript definitions
- **Vite Integration**: Seamless integration with Vite's build system

### Plugin Architecture
- **Main Plugin**: Handles build-time static generation and virtual modules
- **Client Plugin**: Manages client-side hydration runtime inclusion
- **Route Discovery**: Automatic file-based route discovery with custom patterns
- **SSG Renderer**: Server-side rendering engine for React components
- **Page Generator**: Static HTML file generation with asset handling
- **Hydration System**: Selective client-side component hydration

### Configuration Options
```typescript
interface SSGConfig {
  pagesDir?: string           # Pages directory (default: 'src/pages')
  outDir?: string            # Output directory (default: 'dist')
  routes?: (string | SSGRoute)[]  # Explicit routes or auto-discovery
  getRoutes?: () => Promise<SSGRoute[]>  # Custom route discovery
  template?: string          # Custom HTML template
  hydration?: boolean        # Enable client hydration (default: true)
  base?: string             # Base URL (default: '/')
  sitemap?: boolean         # Generate sitemap (default: false)
  render?: CustomRenderFn   # Custom render function
  exclude?: string[]        # Exclude patterns for route discovery
  include?: string[]        # Include patterns for route discovery
}
```

### Client-Side API
- **Hydrate Component**: Mark components for client-side hydration
- **withHydration HOC**: Higher-order component for automatic hydration
- **useHydration Hook**: Hook for hydration state management
- **hydrate Function**: Initialize hydration for specific routes

## Figma Alias Plugin

### Variable Alias Manager
The Figma alias plugin enables creating and managing variable aliases between collections and libraries:

```
packages/figma-alias-plugin/
├── src/
│   ├── components/           # React UI components for alias management
│   ├── types/               # TypeScript type definitions for aliases
│   ├── utils/               # Alias creation and management utilities
│   ├── styles/              # Plugin UI styles
│   ├── plugin.ts            # Main plugin logic (Figma API)
│   ├── ui.tsx               # UI entry point (React)
│   └── ui.html              # HTML template
├── dist/                    # Built plugin files
├── manifest.json            # Figma plugin manifest
└── package.json             # Plugin package config
```

### Features
- **Variable Aliasing**: Create references from one variable to another
- **Cross-Collection Support**: Alias variables between different collections
- **Library Integration**: Support for local and remote library variables
- **Smart Validation**: Prevents circular references and type mismatches
- **Advanced Filtering**: Filter by type, collection, source, alias status
- **Alias Management**: View, organize, and remove existing aliases
- **Mode Support**: Create aliases for specific variable modes

### Alias Types
- **Color → Color**: Reference color values across collections
- **Number → Number**: Reference numeric values (spacing, sizing, etc.)
- **String → String**: Reference text values (font families, etc.)
- **Boolean → Boolean**: Reference boolean values

### Use Cases
- **Design System Consistency**: Create semantic tokens referencing base tokens
- **Cross-Library References**: Reference tokens from central design libraries
- **Theme Switching**: Enable mode-based variable switching
- **Component Overrides**: Local overrides that fall back to library defaults

## Development Workflow

### Commands
```bash
# Development
npm run dev          # Start demo application
npm run dev:ui       # Start UI library in watch mode
npm run dev:ssg      # Start SSG plugin in watch mode
npm run dev:figma    # Start Figma plugin development
npm run dev:figma-alias # Start Figma alias plugin development

# Building
npm run build        # Build UI library
npm run build:demo   # Build demo application
npm run build:ssg    # Build SSG plugin
npm run build:figma  # Build Figma plugin
npm run build:figma-alias # Build Figma alias plugin
npm run build:all    # Build all packages

# Maintenance
npm run clean        # Clean all packages
npm run typecheck    # Type check all packages
```

### Testing Strategy
- **Demo Application**: Serves as integration test and documentation
- **Manual Testing**: Interactive testing through demo components
- **Type Safety**: Comprehensive TypeScript coverage

## API Patterns

### Component Props
```typescript
interface ComponentProps extends React.HTMLAttributes<HTMLElement> {
  variant?: 'default' | 'primary' | 'secondary'
  size?: 'sm' | 'md' | 'lg'
  className?: string
  children?: React.ReactNode
}

// Typography component example
interface TypographyProps extends React.HTMLAttributes<HTMLElement> {
  variant?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'body1' | 'body2' | 'subtitle1' | 'subtitle2' | 'caption' | 'button' | 'overline' | 'inherit'
  color?: 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info' | 'textPrimary' | 'textSecondary' | 'textDisabled' | 'inherit'
  align?: 'left' | 'center' | 'right' | 'justify' | 'inherit'
  component?: React.ElementType
  gutterBottom?: boolean
  noWrap?: boolean
  paragraph?: boolean
}
```

### Theme Usage
```typescript
// Using theme context
const { theme } = useTheme()
const primaryColor = useColorValue('primary.500')

// Using semantic tokens
const { semanticTokens } = useSemanticTokens()
```

### Styling Patterns
```typescript
// Component with variants
const Component = ({ variant, size, className, ...props }) => {
  return (
    <div
      className={cn(
        componentVariants({ variant, size }),
        className
      )}
      {...props}
    />
  )
}
```

This architecture provides a scalable, maintainable, and developer-friendly component library with excellent TypeScript support and modern styling capabilities.
